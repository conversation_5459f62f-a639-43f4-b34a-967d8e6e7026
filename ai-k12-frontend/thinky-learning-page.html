<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thinky Learning Philosophy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(to bottom, #ffffff 0%, #ffffff 40%, #4a5fc1 40%, #4a5fc1 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .title {
            color: #4a5fc1;
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            color: #6b7280;
            font-size: 22px;
            font-weight: 400;
            margin-bottom: 40px;
            letter-spacing: -0.3px;
        }

        .illustration-container {
            position: relative;
            margin-bottom: -80px;
            z-index: 10;
        }

        .illustration {
            width: 100%;
            max-width: 500px;
            height: auto;
            display: block;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        .bottom-section {
            background: transparent;
            padding-top: 120px;
            position: relative;
            z-index: 1;
        }

        .bottom-title {
            color: white;
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            letter-spacing: -0.5px;
        }

        .bottom-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 20px;
            font-weight: 400;
            letter-spacing: -0.2px;
        }

        /* Curved separator */
        .curve-separator {
            position: absolute;
            top: 38%;
            left: 0;
            right: 0;
            height: 100px;
            background: #4a5fc1;
            z-index: 0;
        }

        .curve-separator::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            right: 0;
            height: 100px;
            background: white;
            border-radius: 0 0 50% 50% / 0 0 100px 100px;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 32px;
            }

            .subtitle {
                font-size: 18px;
            }

            .bottom-title {
                font-size: 36px;
            }

            .bottom-subtitle {
                font-size: 18px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="title">Beyond the Answer, Towards Thinking</h1>
        <p class="subtitle">An AI tutor that teaches how to think, not just answer.</p>

        <div class="illustration-container">
            <img src="public/images/learner.png" alt="AI Learning Illustration" class="illustration" />
        </div>
    </div>

    <div class="curve-separator"></div>

    <div class="bottom-section">
        <h2 class="bottom-title">Thinky Learning Philosophy</h2>
        <p class="bottom-subtitle">Answers are not the destination, thinking is the true beginning</p>
    </div>

    <script>
        // Note: Since I cannot directly embed the actual image from your upload,
        // you would need to replace the src attribute of the illustration img tag
        // with the actual image URL or base64 encoded image data.

        // Placeholder for the actual image - in production, you would use:
        // document.querySelector('.illustration').src = 'path-to-your-image.png';

        // Animation on load
        window.addEventListener('load', function () {
            document.querySelector('.container').style.opacity = '0';
            document.querySelector('.bottom-section').style.opacity = '0';

            setTimeout(() => {
                document.querySelector('.container').style.transition = 'opacity 0.6s ease-in';
                document.querySelector('.container').style.opacity = '1';
            }, 100);

            setTimeout(() => {
                document.querySelector('.bottom-section').style.transition = 'opacity 0.6s ease-in';
                document.querySelector('.bottom-section').style.opacity = '1';
            }, 300);
        });
    </script>
</body>

</html>