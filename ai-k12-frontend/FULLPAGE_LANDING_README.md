# FullPage.js Landing Page

这个项目现在包含了一个使用 fullPage.js 创建的新 landing 页面，提供了全屏滚动的用户体验。

## 新增功能

### 1. fullPage.js 集成
- 已安装 fullPage.js 库 (`npm install fullpage.js`)
- 创建了两个新的 Vue 组件：
  - `FullPageLanding.vue` - 完整功能版本
  - `SimpleFullPageLanding.vue` - 简化版本

### 2. 路由配置
新增了以下路由：
- `/fullpage` - 完整的 fullPage.js landing 页面
- `/simple-fullpage` - 简化版的 fullPage.js landing 页面

### 3. 页面特性

#### 完整版本 (`/fullpage`)
- 8个全屏滚动区域：
  1. Hero Section - 主标题和介绍
  2. Talk - 对话学习功能
  3. Think - 思考训练功能  
  4. Teach - 教学功能
  5. Test - 测试功能
  6. Track - 进度跟踪功能
  7. Study Tools - 学习工具套件
  8. CTA - 行动号召

#### 简化版本 (`/simple-fullpage`)
- 6个全屏滚动区域
- 更简洁的布局和内容
- 更快的加载速度

### 4. fullPage.js 配置
- 右侧导航点
- 导航提示文字
- 平滑滚动动画
- 键盘导航支持
- 响应式设计
- 自定义颜色主题

## 使用方法

### 开发环境
```bash
npm run dev
```

然后访问：
- http://localhost:3000/fullpage (完整版)
- http://localhost:3000/simple-fullpage (简化版)
- http://localhost:3000/fullpage-test.html (纯HTML测试版)

### 生产环境
```bash
npm run build
npm run preview
```

## 技术特性

### fullPage.js 配置选项
- `navigation: true` - 显示右侧导航点
- `navigationPosition: 'right'` - 导航位置
- `navigationTooltips` - 导航提示文字
- `showActiveTooltip: true` - 显示活动提示
- `css3: true` - 使用CSS3动画
- `scrollingSpeed: 700` - 滚动速度
- `autoScrolling: true` - 自动滚动
- `fitToSection: true` - 自动适应区域
- `keyboardScrolling: true` - 键盘导航
- `verticalCentered: true` - 垂直居中
- `sectionsColor` - 自定义区域颜色

### 动画效果
- 进入动画：元素淡入和上移
- 离开动画：区域淡出
- 延迟动画：不同元素错开显示

### 响应式设计
- 移动端适配
- 平板端适配
- 桌面端优化

## 文件结构

```
src/
├── components/
│   ├── FullPageLanding.vue          # 完整版fullPage组件
│   ├── SimpleFullPageLanding.vue    # 简化版fullPage组件
│   └── Landing.vue                  # 原始landing页面（保持不变）
├── router/
│   └── index.js                     # 路由配置
└── ...

fullpage-test.html                   # 纯HTML测试页面
```

## 自定义说明

### 修改内容
- 编辑对应的 Vue 组件文件
- 修改 `sectionsColor` 数组来改变背景颜色
- 调整 `navigationTooltips` 数组来修改导航提示

### 添加新区域
1. 在模板中添加新的 `<div class="section">` 
2. 更新 `navigationTooltips` 数组
3. 在 `sectionsColor` 数组中添加对应颜色

### 修改动画
- 调整 `scrollingSpeed` 改变滚动速度
- 修改 CSS 中的 `transition` 属性改变动画效果
- 在 `afterLoad` 回调中自定义进入动画

## 注意事项

1. **原始页面保持不变** - 原来的 Landing.vue 组件和 `/` 路由保持不变
2. **依赖管理** - fullPage.js 已通过 npm 正确安装
3. **浏览器兼容性** - fullPage.js 支持现代浏览器
4. **性能优化** - 简化版本提供更好的性能
5. **移动端体验** - 在移动设备上自动适配为普通滚动

## 下一步

可以考虑的改进：
- 添加更多动画效果
- 集成视频背景
- 添加粒子效果
- 优化移动端体验
- 添加更多交互元素
