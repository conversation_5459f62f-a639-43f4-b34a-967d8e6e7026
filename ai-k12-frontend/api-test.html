<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .file-input {
            margin: 10px 5px;
            padding: 8px;
            border: 2px solid #007bff;
            border-radius: 5px;
            background: white;
            cursor: pointer;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>

<body>
    <h1>🔍 API连接诊断工具</h1>

    <div class="test-section">
        <h3>API基础信息</h3>
        <p><strong>端点:</strong> <span id="apiEndpoint">/whisper/transcribe</span></p>
        <p><strong>状态:</strong> <span id="connectionStatus">未测试</span></p>
    </div>

    <div class="test-section">
        <h3>连接测试</h3>
        <button class="test-button" onclick="testBasicConnection()">基础连接测试</button>
        <button class="test-button" onclick="testWithDummyAudio()">音频格式测试</button>
        <button class="test-button" onclick="testCORS()">CORS测试</button>
        <button class="test-button" onclick="testDifferentMethods()">测试不同HTTP方法</button>
        <button class="test-button" onclick="testParameterNames()">测试参数名</button>
        <div id="connectionResult"></div>
    </div>

    <div class="test-section">
        <h3>文件上传测试</h3>
        <input type="file" id="audioFileInput" accept="audio/*" class="file-input">
        <button class="test-button" onclick="testFileUpload()">上传文件测试</button>
        <div id="fileUploadResult"></div>
    </div>

    <div class="test-section">
        <h3>网络信息</h3>
        <button class="test-button" onclick="showNetworkInfo()">显示网络信息</button>
        <div id="networkInfo"></div>
    </div>

    <script>
        const API_ENDPOINT = (location.origin + '/whisper/transcribe');

        async function testBasicConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试基础连接...</div>';

            try {
                // 尝试不同的请求方法
                let response;
                let method = '';

                try {
                    method = 'OPTIONS';
                    response = await fetch(API_ENDPOINT, {
                        method: 'OPTIONS',
                        mode: 'cors'
                    });
                } catch (e) {
                    try {
                        method = 'HEAD';
                        response = await fetch(API_ENDPOINT, {
                            method: 'HEAD',
                            mode: 'cors'
                        });
                    } catch (e2) {
                        try {
                            method = 'GET';
                            response = await fetch(API_ENDPOINT.replace('/transcribe', '/'), {
                                method: 'GET',
                                mode: 'cors'
                            });
                        } catch (e3) {
                            throw new Error(`所有请求方法都失败了: OPTIONS(${e.message}), HEAD(${e2.message}), GET(${e3.message})`);
                        }
                    }
                }

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 连接成功！\n
                        请求方法: ${method}\n
                        状态码: ${response.status}\n
                        响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}
                    </div>
                `;
                document.getElementById('connectionStatus').textContent = '✅ 连接正常';

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 连接失败！\n
                        错误类型: ${error.name}\n
                        错误信息: ${error.message}\n
                        可能原因:\n
                        1. 后端服务未启动\n
                        2. 端口8001被占用或未开放\n
                        3. 防火墙阻止了连接\n
                        4. 网络连接问题
                    </div>
                `;
                document.getElementById('connectionStatus').textContent = '❌ 连接失败';
            }
        }

        async function testWithDummyAudio() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试音频数据发送...</div>';

            try {
                // 创建一个小的测试音频数据（提高质量）
                const testAudio = new Uint8Array(2048);
                for (let i = 0; i < testAudio.length; i++) {
                    // 生成更真实的音频波形，包含多个频率分量
                    testAudio[i] = Math.sin(i * 0.02) * 80 + Math.sin(i * 0.08) * 40 + Math.sin(i * 0.12) * 20 + 128;
                }

                const audioBlob = new Blob([testAudio], { type: 'audio/wav' });
                const formData = new FormData();
                formData.append('audio_file', audioBlob, 'test.wav');
                formData.append('language', 'zh-CN');

                console.log('发送测试音频数据，大小:', audioBlob.size);

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    body: formData,
                    mode: 'cors'
                });

                const responseData = await response.text();

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 音频数据发送成功！\n
                        状态码: ${response.status}\n
                        响应数据: ${responseData}\n
                        音频数据大小: ${audioBlob.size} bytes
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 音频数据发送失败！\n
                        错误类型: ${error.name}\n
                        错误信息: ${error.message}\n
                        可能原因:\n
                        1. API不支持POST请求\n
                        2. 数据格式不正确\n
                        3. 请求体过大\n
                        4. 后端处理错误
                    </div>
                `;
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试CORS配置...</div>';

            try {
                const response = await fetch(API_ENDPOINT, {
                    method: 'OPTIONS',
                    mode: 'cors',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                const corsHeaders = {};
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().includes('access-control')) {
                        corsHeaders[key] = value;
                    }
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ CORS测试完成！\n
                        状态码: ${response.status}\n
                        CORS头信息: ${JSON.stringify(corsHeaders, null, 2)}
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ CORS测试失败！\n
                        错误信息: ${error.message}\n
                        可能原因:\n
                        1. 后端未配置CORS\n
                        2. 浏览器阻止了跨域请求\n
                        3. 预检请求失败
                    </div>
                `;
            }
        }

        async function testDifferentMethods() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试不同HTTP方法...</div>';

            const methods = ['GET', 'POST', 'PUT', 'PATCH'];
            const results = {};

            for (const method of methods) {
                try {
                    let testData = null;
                    const headers = {};

                    // 为POST/PUT/PATCH方法添加测试数据
                    if (['POST', 'PUT', 'PATCH'].includes(method)) {
                        const testAudio = new Uint8Array(1024);
                        for (let i = 0; i < testAudio.length; i++) {
                            // 生成更真实的音频波形
                            testAudio[i] = Math.sin(i * 0.05) * 64 + Math.sin(i * 0.15) * 32 + 128;
                        }
                        testData = new FormData();
                        testData.append('audio_file', new Blob([testAudio], { type: 'audio/wav' }), 'test.wav');
                        testData.append('language', 'zh-CN');
                    } else {
                        headers['Content-Type'] = 'application/json';
                    }

                    console.log(`测试 ${method} 方法...`);
                    const response = await fetch(API_ENDPOINT, {
                        method: method,
                        body: testData,
                        headers: headers,
                        mode: 'cors'
                    });

                    results[method] = {
                        status: response.status,
                        statusText: response.statusText,
                        success: response.ok
                    };

                } catch (error) {
                    results[method] = {
                        error: error.message,
                        success: false
                    };
                }
            }

            let resultHtml = '📊 HTTP方法测试结果:\n\n';

            methods.forEach(method => {
                const result = results[method];
                if (result.success) {
                    resultHtml += `✅ ${method}: ${result.status} ${result.statusText}\n`;
                } else if (result.error) {
                    resultHtml += `❌ ${method}: ${result.error}\n`;
                } else {
                    resultHtml += `❌ ${method}: ${result.status} ${result.statusText}\n`;
                }
            });

            resultHtml += '\n💡 建议：使用状态码为200的方法';

            resultDiv.innerHTML = `<div class="info">${resultHtml}</div>`;
        }

        async function testParameterNames() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="info">正在测试不同的参数名组合...</div>';

            const audioParamNames = ['audio', 'audio_file', 'file', 'audio_data', 'recording'];
            const languageParamNames = ['language', 'lang', 'language_code', 'locale'];

            const results = {};

            for (const audioParam of audioParamNames) {
                for (const langParam of languageParamNames) {
                    const testName = `${audioParam}+${langParam}`;

                    try {
                        const testAudio = new Uint8Array(1024);
                        for (let i = 0; i < testAudio.length; i++) {
                            // 生成更真实的音频波形
                            testAudio[i] = Math.sin(i * 0.05) * 64 + Math.sin(i * 0.15) * 32 + 128;
                        }

                        const formData = new FormData();
                        formData.append(audioParam, new Blob([testAudio], { type: 'audio/wav' }), 'test.wav');
                        formData.append(langParam, 'zh-CN');

                        console.log(`测试参数组合: ${testName}`);
                        const response = await fetch(API_ENDPOINT, {
                            method: 'POST',
                            body: formData,
                            mode: 'cors'
                        });

                        results[testName] = {
                            status: response.status,
                            success: response.ok,
                            statusText: response.statusText
                        };

                        if (response.ok) {
                            // 如果成功，获取响应内容
                            const responseData = await response.text();
                            results[testName].response = responseData;
                        }

                    } catch (error) {
                        results[testName] = {
                            error: error.message,
                            success: false
                        };
                    }
                }
            }

            let resultHtml = '📊 参数名组合测试结果:\n\n';

            Object.entries(results).forEach(([paramCombo, result]) => {
                if (result.success) {
                    resultHtml += `✅ ${paramCombo}: ${result.status} ${result.statusText}\n`;
                    if (result.response) {
                        resultHtml += `   响应: ${result.response}\n`;
                    }
                } else if (result.error) {
                    resultHtml += `❌ ${paramCombo}: ${result.error}\n`;
                } else {
                    resultHtml += `❌ ${paramCombo}: ${result.status} ${result.statusText}\n`;
                }
            });

            resultHtml += '\n💡 建议：使用返回200状态码的参数组合';

            resultDiv.innerHTML = `<div class="info">${resultHtml}</div>`;
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('audioFileInput');
            const resultDiv = document.getElementById('fileUploadResult');

            if (!fileInput.files || fileInput.files.length === 0) {
                resultDiv.innerHTML = '<div class="error">❌ 请先选择一个音频文件</div>';
                return;
            }

            const file = fileInput.files[0];
            resultDiv.innerHTML = `<div class="info">正在上传文件: ${file.name} (${(file.size / 1024).toFixed(1)} KB)</div>`;

            try {
                const formData = new FormData();
                formData.append('audio_file', file);
                formData.append('language', 'zh-CN');

                console.log('上传文件:', file.name, '大小:', file.size);

                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    body: formData,
                    mode: 'cors'
                });

                const responseData = await response.text();

                if (response.ok) {
                    let transcriptionText = null;
                    let jsonData;

                    try {
                        jsonData = JSON.parse(responseData);
                        if (jsonData && typeof jsonData === 'object' && jsonData.text) {
                            transcriptionText = jsonData.text;
                        } else if (jsonData && typeof jsonData === 'string' && jsonData.trim()) {
                            transcriptionText = jsonData.trim();
                        }
                    } catch (e) {
                        // 如果不是JSON，可能是纯文本
                        if (responseData && responseData.trim()) {
                            transcriptionText = responseData.trim();
                        }
                    }

                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 文件上传成功！\n
                            状态码: ${response.status}\n
                            原始响应: ${responseData}\n
                            转录结果: ${transcriptionText || '无'}
                        </div>
                    `;
                }
            } else {
                resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 文件上传失败！\n
                            状态码: ${response.status}\n
                            响应: ${responseData}
                        </div>
                    `;
            }

        } catch (error) {
            resultDiv.innerHTML = `
                    <div class="error">
                        ❌ 文件上传异常！\n
                        错误: ${error.message}
                    </div>
                `;
        }
        }

        function showNetworkInfo() {
            const infoDiv = document.getElementById('networkInfo');
            infoDiv.innerHTML = `
                <div class="info">
                    📊 网络信息\n
                    当前URL: ${window.location.href}\n
                    协议: ${window.location.protocol}\n
                    主机: ${window.location.host}\n
                    端口: ${window.location.port || '80/443'}\n
                    API端点: ${API_ENDPOINT}\n
                    用户代理: ${navigator.userAgent}\n
                    在线状态: ${navigator.onLine ? '在线' : '离线'}\n
                    Cookie启用: ${navigator.cookieEnabled ? '是' : '否'}
                </div>
            `;
        }

        // 页面加载时自动显示网络信息
        showNetworkInfo();
    </script>
</body>

</html>
