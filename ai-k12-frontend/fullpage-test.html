<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FullPage.js Test - Thinky AI</title>
    <link rel="stylesheet" href="node_modules/fullpage.js/dist/fullpage.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
        }

        /* Navigation */
        .fp-nav {
            position: fixed !important;
            top: 20px !important;
            left: 20px !important;
            right: auto !important;
            z-index: 1000 !important;
        }

        .nav-brand {
            display: flex;
            align-items: center;
        }

        .logo-image {
            height: 48px;
            width: auto;
            object-fit: contain;
        }

        /* Override fullPage navigation styles */
        #fp-nav ul li a span {
            background: rgba(255, 255, 255, 0.5) !important;
            width: 8px !important;
            height: 8px !important;
        }

        #fp-nav ul li a.active span {
            background: #3F51B5 !important;
            width: 12px !important;
            height: 12px !important;
        }

        #fp-nav ul li .fp-tooltip {
            background: #3F51B5 !important;
            color: white !important;
            font-size: 12px !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
        }

        /* Section base styles */
        .section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            position: relative;
            overflow: hidden;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            max-width: 1200px;
            width: 100%;
        }

        .hero-title {
            font-weight: 800;
            font-size: clamp(32px, 4vw, 56px);
            line-height: 1.2;
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-weight: 500;
            font-size: clamp(16px, 2vw, 24px);
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .cta-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 16px 32px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .hero-image {
            width: 100%;
            height: auto;
            max-width: 400px;
        }

        /* Feature Sections */
        .section-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            max-width: 1200px;
            width: 100%;
        }

        .feature-title {
            font-weight: 700;
            font-size: clamp(48px, 6vw, 80px);
            color: #535353;
            margin-bottom: 20px;
            line-height: 1;
        }

        .feature-subtitle {
            font-weight: 600;
            font-size: clamp(20px, 2.5vw, 28px);
            color: #000;
            margin-bottom: 16px;
        }

        .feature-description {
            font-weight: 400;
            font-size: clamp(14px, 1.5vw, 18px);
            color: #535353;
            line-height: 1.6;
        }

        .feature-image {
            width: 100%;
            height: auto;
            border-radius: 12px;
        }

        /* Think Section */
        .think-section .feature-title,
        .think-section .feature-subtitle,
        .think-section .feature-description {
            color: #2F3348;
        }

        /* Test Section */
        .test-section .feature-title,
        .test-section .feature-subtitle,
        .test-section .feature-description {
            color: white;
        }

        /* Teach Section */
        .teach-graph-visual {
            background: url('/images/math-chart.png') center/contain no-repeat;
            width: 100%;
            height: 400px;
            border-radius: 12px;
            background-color: #f0f0f0;
        }

        /* CTA Section */
        .cta-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            max-width: 1200px;
            width: 100%;
        }

        .cta-title {
            font-weight: 700;
            font-size: clamp(32px, 4vw, 48px);
            color: white;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .cta-subtitle {
            color: white;
            opacity: 0.9;
            font-size: clamp(16px, 2vw, 20px);
            margin-bottom: 32px;
            line-height: 1.4;
        }

        .cta-button-large {
            background: rgba(59, 130, 246, 0.2);
            color: white;
            border: 2px solid #60a5fa;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: clamp(16px, 2vw, 20px);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-block;
        }

        .cta-button-large:hover {
            background: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
        }

        .character-bottom {
            width: 100%;
            height: 300px;
            background: url('/images/character-bottom.png') center/contain no-repeat;
            opacity: 0.9;
            background-color: #f0f0f0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .section {
                padding: 0 20px;
            }
            
            .hero-content,
            .section-content,
            .cta-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div id="fullpage">
        <!-- Section 1: Hero -->
        <div class="section hero-section">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">Beyond the Answer, Towards Thinking</h1>
                    <p class="hero-subtitle">An AI tutor that teaches how to think, not just answer.</p>
                    <a href="#" class="cta-button">Start For Free</a>
                </div>
                <div class="hero-visual">
                    <img src="/images/learner.png" alt="Learner" class="hero-image" />
                </div>
            </div>
        </div>

        <!-- Section 2: Talk -->
        <div class="section talk-section">
            <div class="section-content">
                <div class="feature-visual">
                    <img src="/images/thinky-talk-prototype2.png" alt="Talk Feature" class="feature-image" />
                </div>
                <div class="feature-text">
                    <h2 class="feature-title">Talk</h2>
                    <h3 class="feature-subtitle">Learn through dialogue</h3>
                    <p class="feature-description">Smooth conversations that feel like a real teacher guiding you.</p>
                </div>
            </div>
        </div>

        <!-- Section 3: Think -->
        <div class="section think-section">
            <div class="section-content">
                <div class="feature-text">
                    <h2 class="feature-title">Think</h2>
                    <h3 class="feature-subtitle">Think before the answer</h3>
                    <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
                </div>
                <div class="feature-visual">
                    <img src="/images/thinky-think-prototype.png" alt="Think Feature" class="feature-image" />
                </div>
            </div>
        </div>

        <!-- Section 4: Teach -->
        <div class="section teach-section">
            <div class="section-content">
                <div class="feature-visual">
                    <div class="teach-graph-visual"></div>
                </div>
                <div class="feature-text">
                    <h2 class="feature-title">Teach</h2>
                    <h3 class="feature-subtitle">Turn complexity into clarity</h3>
                    <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
                </div>
            </div>
        </div>

        <!-- Section 5: Test -->
        <div class="section test-section">
            <div class="section-content">
                <div class="feature-text">
                    <h2 class="feature-title">Test</h2>
                    <h3 class="feature-subtitle">Spot weaknesses fast</h3>
                    <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
                </div>
                <div class="feature-visual">
                    <img src="/images/thinky-test-prototype.png" alt="Test Feature" class="feature-image" />
                </div>
            </div>
        </div>

        <!-- Section 6: CTA -->
        <div class="section cta-section">
            <div class="cta-content">
                <div class="cta-text">
                    <h2 class="cta-title">Ready to ace the study?</h2>
                    <p class="cta-subtitle">Sign up to revolutionise your learning!</p>
                    <a href="#" class="cta-button-large">Start For Free</a>
                </div>
                <div class="cta-visual">
                    <div class="character-bottom"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="fp-nav">
        <div class="nav-brand">
            <img src="/images/logo1.png" alt="Thinky Logo" class="logo-image" />
        </div>
    </nav>

    <script src="node_modules/fullpage.js/dist/fullpage.min.js"></script>
    <script>
        new fullpage('#fullpage', {
            // Navigation
            navigation: true,
            navigationPosition: 'right',
            navigationTooltips: ['Hero', 'Talk', 'Think', 'Teach', 'Test', 'Get Started'],
            showActiveTooltip: true,
            
            // Scrolling
            css3: true,
            scrollingSpeed: 700,
            autoScrolling: true,
            fitToSection: true,
            fitToSectionDelay: 1000,
            scrollBar: false,
            easing: 'easeInOutCubic',
            easingcss3: 'ease',
            
            // Design
            sectionsColor: ['#ffffff', '#ffffff', '#E3E9FA', '#ffffff', '#3F51B5', '#1e3a8a'],
            fixedElements: '.fp-nav',
            
            // Custom selectors
            sectionSelector: '.section',
            
            // Events
            lazyLoading: true,
            observer: true,
            credits: { enabled: false }
        });
    </script>
</body>
</html>
