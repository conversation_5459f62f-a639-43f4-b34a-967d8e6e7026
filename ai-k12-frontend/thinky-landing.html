<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thinky - Beyond the Answer, Towards Thinking</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #000000;
            overflow-x: hidden;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #E5E7EB;
            padding: 17.78px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            height: 68.82px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            display: flex;
            align-items: center;
            position: relative;
        }

        .logo-squares {
            position: relative;
            width: 19.12px;
            height: 19.39px;
        }

        .logo-square {
            position: absolute;
            border-radius: 1px;
        }

        .logo-square-1 {
            width: 5.76px;
            height: 5.77px;
            background: #F58147;
            top: 8.22px;
            left: 0.02px;
        }

        .logo-square-2 {
            width: 9.02px;
            height: 9.96px;
            background: #7BABB1;
            top: 0px;
            left: 3.11px;
        }

        .logo-square-3 {
            width: 8.92px;
            height: 8.21px;
            background: #2261B3;
            top: 6.94px;
            left: 7.11px;
        }

        .logo-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 21.41px;
            color: #000000;
            margin-left: 11px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 3.56px;
        }

        .nav-item {
            padding: 7.11px 17.78px;
            border-radius: 5.34px;
            color: #3C315B;
            font-weight: 500;
            font-size: 14.23px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(63, 81, 181, 0.1);
            color: #3F51B5;
        }

        .cta-button {
            background: #3F51B5;
            color: white;
            padding: 8px 15.12px;
            border-radius: 8891px;
            text-decoration: none;
            font-weight: 500;
            font-size: 14.23px;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: #324A77;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            position: relative;
            background: transparent;
            padding: 69px 0 0; /* align exactly under fixed navbar */
            overflow: visible; /* allow nav to overlap into next section */
        }

        .hero-container {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 0; /* full-bleed */
            position: relative;
        }

        .hero-title {
            text-align: center;
            margin-bottom: 67px;
            padding-top: 113.82px;
        }

        .hero-title h1 {
            font-family: 'Roboto', sans-serif;
            font-weight: 900;
            font-size: clamp(32px, 5vw, 48.91px);
            line-height: 1.21;
            color: #3F51B5;
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            font-size: clamp(18px, 3vw, 34.91px);
            color: #3F51B5;
            text-align: center;
            margin-bottom: 68px;
        }

        .hero-image {
            position: relative;
            width: 100%;
            max-width: none;
            margin: 0;
            border-radius: 0;
            overflow: visible;
            background: transparent;
            padding: 0;
        }

        .hero-prototype-image {
            display: block;
            width: 100%;
            height: auto;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
            background: transparent;
            backdrop-filter: none;
            padding: 60px 20px;
            border-radius: 20px;
            max-width: 800px;
        }

        .learning-philosophy {
            font-family: 'Arial', sans-serif;
            font-weight: 700;
            font-size: clamp(28px, 4vw, 56.91px);
            color: #FFFFFF;
            margin-bottom: 20px;
        }

        .philosophy-subtitle {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 300;
            font-size: clamp(16px, 2vw, 25.44px);
            color: #FFFFFF;
        }

        .character-image { display: none; }

        /* Feature Sections */
        .feature-section {
            padding: 120px 0;
            position: relative;
            background: #FFFFFF; /* set default; specific sections override */
        }

        .section-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 100px;
            align-items: center;
        }

        .feature-content h2 {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: clamp(60px, 8vw, 118.7px);
            color: #535353;
            margin-bottom: 30px;
            line-height: 1;
        }

        .feature-subtitle {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: clamp(24px, 3vw, 34.06px);
            color: #000000;
            margin-bottom: 20px;
        }

        .feature-description {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 500;
            font-size: clamp(16px, 2vw, 25.54px);
            color: #535353;
            line-height: 1.43;
        }

        .feature-visual {
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Track Section (custom illustration) */
        .track-visual { position: relative; height: 420px; }
        .track-canvas { position: relative; width: 100%; height: 100%; }
        .track-card { position: absolute; background: #FFFFFF; border: 2px solid #5875FF; border-radius: 22px; box-shadow: 0 16px 36px rgba(76,111,255,0.16); }
        /* Detection cards group (top-left outer box) */
        .track-detections { left: 5%; top: 6%; width: 56%; padding: 16px 18px 10px; }
        .det-pie { position: absolute; left: -32px; top: 16px; width: 74px; height: 74px; border-radius: 50%; background: #FFFFFF; border: 2px solid #5875FF; box-shadow: 0 10px 24px rgba(76,111,255,0.16); display:flex; align-items:center; justify-content:center; font-size: 11px; color:#5875FF; text-align:center; line-height:1.1; }
        .det-wrapper { margin-left: 60px; }
        .det-card { background: #FFFFFF; border: 1.5px solid #C8D8FF; border-radius: 16px; padding: 14px 16px; margin-bottom: 12px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); }
        .det-title { display: inline-flex; align-items: center; gap: 8px; font-weight: 800; font-size: 14px; color: #2E61E6; margin-bottom: 10px; }
        .det-bullet { width: 10px; height: 10px; border-radius: 50%; }
        .det-row { display: flex; align-items: center; gap: 10px; margin-top: 6px; }
        .star { width: 10px; height: 10px; transform: rotate(45deg); background: linear-gradient(180deg, #FFD277 0%, #FFB347 100%); border-radius: 2px; display: inline-block; }
        .slider-pill { position: absolute; right: -26px; top: 58px; background: #EAF1FF; border: 2px solid #6B8BFF; border-radius: 999px; padding: 8px 18px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); display:flex; gap:10px; }
        .slider-dot { width: 12px; height: 12px; border-radius: 50%; background: #6B8BFF; box-shadow: inset 0 0 0 3px #FFFFFF; }
        /* Chart card (bottom-left) */
        .track-chart { left: 3%; bottom: 4%; width: 62%; padding: 18px 20px 20px; }
        .chart-header-pill { position: absolute; top: -16px; left: 40px; background: #EAF1FF; border: 2px solid #6B8BFF; color: #6B8BFF; padding: 8px 22px; border-radius: 999px; font-size: 12px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); }
        .gauge { width: 96px; height: 96px; border: 2px dashed #CAD7FF; border-radius: 50%; position: relative; }
        .gauge::after { content: ''; position: absolute; width: 58px; height: 58px; border-top: 6px solid #4C6FFF; border-right: 6px solid transparent; border-radius: 50%; transform: rotate(-20deg); top: 14px; left: 14px; }
        .info-lines { flex:1; padding-top: 6px; }
        .mini-line { height: 4px; background: linear-gradient(90deg, #6B8BFF, #46C2C2); border-radius: 6px; margin: 8px 0; opacity: .9; }
        .bars { display: flex; align-items: end; gap: 12px; padding: 12px 8px 0; }
        .bar { width: 24px; background: #10B2A8; border-radius: 6px; }
        .bar:nth-child(1){ height: 44px; }
        .bar:nth-child(2){ height: 64px; }
        .bar:nth-child(3){ height: 40px; }
        .bar:nth-child(4){ height: 78px; }
        .chart-star { position: absolute; right: 22px; top: -12px; width: 14px; height: 14px; transform: rotate(45deg); background: linear-gradient(180deg, #FFD277 0%, #FFB347 100%); border-radius: 2px; }

        /* Talk Section */
        .talk-section { padding: 0; }
        .talk-visual { display: none; }
        .talk-image { width: 100%; max-width: none; margin: 0; }
        .talk-prototype-image { display: block; width: 100%; height: auto; }

        /* Teach Section - network graph image */
        .teach-graph-visual {
            background: url('/images/math-chart.png') left center/contain no-repeat;
            width: 100%;
            height: 460px;
        }
        .teach-section .section-container { grid-template-columns: 1.3fr 1fr; gap: 80px; }

        /* Think Section - Special styling */
        .think-section {
            background: #E3E9FA;
        }

        .think-section h2 {
            color: #535353;
        }

        .think-section .feature-subtitle {
            color: #363535;
        }

        .think-section .feature-description {
            color: #535353;
        }

        /* Test Section - Dark theme */
        .test-section {
            background: #3F51B5;
            color: white;
        }

        .test-section h2,
        .test-section .feature-subtitle,
        .test-section .feature-description {
            color: white;
        }

        /* Navigation Pills */
        .feature-nav-wrapper {
            width: auto;
            display: flex;
            justify-content: center;
            position: absolute;
            left: 50%;
            bottom: -44px; /* float between section 1 and 2 */
            transform: translateX(-50%);
            margin: 0;
            z-index: 4;
        }

        .feature-nav {
            position: relative;
            background: transparent;
            border: 0;
            box-shadow: none;
            border-radius: 0;
            padding: 0;
            display: inline-flex;
            gap: 28px;
            align-items: flex-start;
        }

        .nav-pill {
            display: inline-block;
            padding: 0;
            margin: 0;
            background: transparent;
            border: 0;
            border-radius: 0;
            text-decoration: none;
            line-height: 0;
        }
        .nav-pill:hover { transform: translateY(-1px); }
        .nav-pill.active { filter: brightness(1.0); }
        .nav-pill-image { display: block; width: auto; height: 72px; pointer-events: none; }

        /* Study Tool Suite (two-column) */
        .study-tools { background: #E3E9FA; padding: 120px 0; }
        .study-suite { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: grid; grid-template-columns: 1.2fr 1fr; gap: 60px; align-items: start; }
        .suite-left { display: flex; flex-direction: column; gap: 16px; }
        .suite-heading { font-family: 'Inter', sans-serif; font-weight: 800; letter-spacing: -0.02em; font-size: clamp(40px, 6vw, 64px); color: #53575F; }
        .suite-subtitle { font-family: 'Inter', sans-serif; font-weight: 400; font-size: clamp(16px, 2vw, 22px); color: #5B6279; line-height: 1.6; }
        .suite-subtitle .highlight { background: linear-gradient(90deg, #4C6FFF 0%, #06B6D4 100%); -webkit-background-clip: text; background-clip: text; color: transparent; font-weight: 700; }
        .suite-illustration { margin-top: 22px; width: 100%; height: 340px; border-radius: 18px; background: radial-gradient(60% 60% at 20% 30%, rgba(76,111,255,0.18) 0%, rgba(76,111,255,0) 70%), url('/images/product-screenshot-1-1b6bf1.png') center/contain no-repeat; box-shadow: 0 24px 60px rgba(76,111,255,0.25); }
        .suite-right { padding-top: 18px; }
        .suite-list { display: grid; gap: 18px; }
        .suite-item { display: grid; grid-template-columns: 48px 1fr; gap: 14px; align-items: start; }
        .suite-badge { width: 48px; height: 48px; border-radius: 12px; border: 2px solid #4C6FFF; background: #F4F7FF; display: inline-flex; align-items: center; justify-content: center; color: #4C6FFF; font-size: 22px; box-shadow: 0 6px 16px rgba(76,111,255,0.15); }
        .suite-item h4 { margin: 0; font-family: 'Inter', sans-serif; font-weight: 800; font-size: 20px; color: #2F3348; }
        .suite-item p { margin-top: 6px; font-size: 14px; color: #5F6783; line-height: 1.5; }

        /* CTA Section */
        .cta-section {
            background: #3F51B5;
            padding: 120px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-content h2 {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: clamp(36px, 5vw, 61.24px);
            color: white;
            margin-bottom: 30px;
        }

        .cta-subtitle {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 300;
            font-size: clamp(20px, 3vw, 34.44px);
            color: white;
            margin-bottom: 50px;
        }

        .cta-button-large {
            background: rgba(117, 152, 255, 0.3);
            color: white;
            padding: 22px 81px;
            border-radius: 41px;
            border: 2px solid white;
            text-decoration: none;
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: clamp(20px, 3vw, 34.44px);
            display: inline-block;
            transition: all 0.3s ease;
        }

        .cta-button-large:hover {
            background: white;
            color: #3F51B5;
            transform: translateY(-3px);
        }

        .character-bottom { position: absolute; right: 50px; bottom: 0; width: 380px; height: 360px; background: url('/images/character-bottom.png') bottom right/contain no-repeat; opacity: 0.9; }

        /* Footer */
        .footer {
            background: #111827;
            color: white;
            padding: 57px 0 28px;
        }

        .footer-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 28px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 214px;
            margin-bottom: 43px;
        }

        .footer-brand {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .footer-brand .logo-text {
            color: white;
        }

        .footer-tagline {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14.23px;
            color: #9CA3AF;
            margin-bottom: 30px;
        }

        .social-links {
            display: flex;
            gap: 14px;
        }

        .social-link {
            width: 35.57px;
            height: 35.57px;
            background: #1F2937;
            border-radius: 14.23px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: #3F51B5;
            transform: translateY(-2px);
        }

        .download-section h3 {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 16.01px;
            color: white;
            margin-bottom: 14px;
        }

        .download-description {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14.23px;
            color: #9CA3AF;
            margin-bottom: 14px;
        }

        .download-buttons {
            display: flex;
            flex-direction: column;
            gap: 10.67px;
        }

        .download-button {
            background: #1F2937;
            border-radius: 7.11px;
            padding: 10.67px;
            display: flex;
            align-items: center;
            gap: 10.67px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }

        .download-button:hover {
            background: #374151;
            transform: translateY(-2px);
        }

        .download-button i {
            font-size: 21.34px;
        }

        .download-info {
            display: flex;
            flex-direction: column;
        }

        .download-type {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 10.67px;
            color: #9CA3AF;
        }

        .download-store {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 12.45px;
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #1F2937;
            padding-top: 29.34px;
            text-align: center;
        }

        .copyright {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 12.45px;
            color: #6B7280;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                padding: 15px;
                height: auto;
                flex-direction: column;
                gap: 15px;
            }

            .nav-menu {
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }

            .hero { padding-top: 90px; }

            .hero-image { padding: 0; }

            .hero-content { display: none; }

            .section-container {
                grid-template-columns: 1fr;
                gap: 50px;
                text-align: center;
            }

            .feature-nav-wrapper { position: static; transform: none; margin: 18px 20px 0; }
            .feature-nav { width: 100%; justify-content: center; padding: 8px 0; gap: 16px; flex-wrap: wrap; }
            .nav-pill-image { height: 56px; }

            .study-suite { grid-template-columns: 1fr; gap: 28px; }
            .suite-illustration { height: 260px; }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 50px;
                text-align: center;
            }

            .character-bottom {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .logo-text {
                font-size: 18px;
            }

            .hero-title h1 {
                font-size: 28px;
            }

            .hero-subtitle {
                font-size: 16px;
            }

            .feature-content h2 {
                font-size: 40px;
            }

            .cta-button-large {
                padding: 18px 40px;
                font-size: 20px;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Parallax effect for hero background */
        .hero-bg-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(233, 251, 255, 0.9) 0%, rgba(255, 255, 255, 0.9) 100%);
            pointer-events: none;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="logo">
            <div class="logo-icon">
                <div class="logo-squares">
                    <div class="logo-square logo-square-1"></div>
                    <div class="logo-square logo-square-2"></div>
                    <div class="logo-square logo-square-3"></div>
                </div>
            </div>
            <div class="logo-text">Thinky</div>
        </div>

        <div class="nav-menu">
            <a href="#tools" class="nav-item">
                Product
                <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 10px;"></i>
            </a>
            <a href="#about" class="nav-item">
                About us
                <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 10px;"></i>
            </a>
            <a href="/home" target="_top" class="cta-button">Start For Free</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="hero">
        <div class="hero-container">
            <div class="hero-image animate-fade-in-up no-parallax">
                <img class="hero-prototype-image" src="/images/thinky-hero-prototype.png" alt="Thinky hero prototype" />
            </div>
            <div class="feature-nav-wrapper">
                <nav class="feature-nav">
                    <a href="#talk" class="nav-pill" data-section="talk" aria-label="Talk" data-img="/images/nav-talk.svg" data-img-active="/images/nav-talk-active.svg">
                        <img class="nav-pill-image" src="/images/nav-talk.svg" alt="Talk" />
                    </a>
                    <a href="#think" class="nav-pill active" data-section="think" aria-label="Think" data-img="/images/nav-think.svg" data-img-active="/images/nav-think-active.svg">
                        <img class="nav-pill-image" src="/images/nav-think-active.svg" alt="Think" />
                    </a>
                    <a href="#teach" class="nav-pill" data-section="teach" aria-label="Teach" data-img="/images/nav-teach.svg" data-img-active="/images/nav-teach-active.svg">
                        <img class="nav-pill-image" src="/images/nav-teach.svg" alt="Teach" />
                    </a>
                    <a href="#test" class="nav-pill" data-section="test" aria-label="Test" data-img="/images/nav-test.svg" data-img-active="/images/nav-test-active.svg">
                        <img class="nav-pill-image" src="/images/nav-test.svg" alt="Test" />
                    </a>
                    <a href="#track" class="nav-pill" data-section="track" aria-label="Track" data-img="/images/nav-track.svg" data-img-active="/images/nav-track-active.svg">
                        <img class="nav-pill-image" src="/images/nav-track.svg" alt="Track" />
                    </a>
                </nav>
            </div>
        </div>
    </section>

    

    

    <!-- Talk Section -->
    <section class="feature-section talk-section" id="talk">
        <div class="talk-image">
            <img class="talk-prototype-image" src="/images/thinky-talk-prototype.png" alt="Talk section prototype" />
        </div>
    </section>

    <!-- Think Section -->
    <section class="feature-section think-section" id="think">
        <div class="section-container">
            <div class="feature-content animate-fade-in-up">
                <h2>Think</h2>
                <h3 class="feature-subtitle">Think before the answer</h3>
                <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
            </div>
            <div class="feature-visual animate-fade-in-up">
                <div
                    style="background: rgba(182, 200, 255, 0.3); border-radius: 20px; padding: 40px; text-align: center;">
                    <div
                        style="background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <h4 style="color: #233058; margin-bottom: 15px; font-size: 14px; font-weight: 700;">Which
                            ancient civilization built the Great Pyramid of Giza?</h4>
                        <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 20px;">
                            <button
                                style="padding: 8px 16px; border: 1px solid #DFE6FF; background: #F6F8FF; border-radius: 13px; color: #2F3250; font-size: 10px;">Ancient
                                Romans</button>
                            <button
                                style="padding: 8px 16px; border: 1px solid #B9C8FF; background: #E7ECFF; border-radius: 13px; color: #1C2E8F; font-size: 10px; font-weight: 700;">Ancient
                                Egyptians</button>
                            <button
                                style="padding: 8px 16px; border: 1px solid #DFE6FF; background: #F6F8FF; border-radius: 13px; color: #2F3250; font-size: 10px;">Ancient
                                Greeks</button>
                        </div>
                        <div style="display: flex; gap: 5px; justify-content: center;">
                            <span
                                style="background: #E9F6FF; color: #0E5C9B; padding: 4px 8px; border-radius: 10px; font-size: 9px;">Step
                                1 · Identify context</span>
                            <span
                                style="background: #F6F8FF; color: #4B4F6B; padding: 4px 8px; border-radius: 10px; font-size: 9px;">Step
                                2 · Compare options</span>
                            <span
                                style="background: #F6F8FF; color: #4B4F6B; padding: 4px 8px; border-radius: 10px; font-size: 9px;">Step
                                3 · Verify evidence</span>
                        </div>
                    </div>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <div
                            style="background: rgba(255,255,255,0.9); border: 1px solid #354EA9; border-radius: 7px; padding: 15px; text-align: left; font-size: 11px;">
                            <div style="color: #79A1FF; font-weight: 700; margin-bottom: 5px;">💡 Hint</div>
                            <div style="color: #6B7292;">Link "pyramids" to dynasties.</div>
                        </div>
                        <div
                            style="background: rgba(255,255,255,0.9); border: 1px solid #354EA9; border-radius: 7px; padding: 15px; text-align: left; font-size: 11px;">
                            <div style="color: #79A1FF; font-weight: 700; margin-bottom: 5px;">🤔 What if…</div>
                            <div style="color: #6B7292;">the source mentions pharaohs?</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <!-- Teach Section (image version) -->
    <section class="feature-section teach-section" id="teach">
        <div class="section-container">
            <div class="feature-visual animate-fade-in-up">
                <div class="teach-graph-visual"></div>
            </div>
            <div class="feature-content animate-fade-in-up">
                <h2>Teach</h2>
                <h3 class="feature-subtitle">Turn complexity into clarity</h3>
                <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
            </div>
        </div>
    </section>

    <!-- Test Section -->
    <section class="feature-section test-section" id="test">
        <div class="section-container">
            <div class="feature-content animate-fade-in-up">
                <h2>Test</h2>
                <h3 class="feature-subtitle">Spot weaknesses fast</h3>
                <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
            </div>
            <div class="feature-visual animate-fade-in-up">
                <div
                    style="background: rgba(255,255,255,0.1); border: 3px solid #4F6BFF; border-radius: 25px; padding: 30px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; border: 1px solid #B9C7D8; border-radius: 15px; padding: 20px;">
                            <div style="color: #2D2C3A; font-weight: 700; font-size: 13px; margin-bottom: 15px;">Error
                                Bank</div>
                            <div style="margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                                    <div style="width: 12px; height: 12px; background: #FF5A5F; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">x — 1 = 0 → wrong isolation
                                        step</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                                    <div style="width: 12px; height: 12px; background: #FF5A5F; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">Quadratic factoring → sign
                                        error</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                    <div style="width: 12px; height: 12px; background: #FFB020; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">Units mismatch in final answer</span>
                                </div>
                                <button
                                    style="background: #4766FF; color: white; border: none; padding: 5px 15px; border-radius: 10px; font-size: 9px; margin-right: 10px;">Retry</button>
                                <button
                                    style="background: #EEF3FF; color: #4766FF; border: none; padding: 5px 15px; border-radius: 10px; font-size: 8px;">Hint:
                                    review sign patterns (± ±)</button>
                            </div>
                        </div>
                        <div style="background: white; border: 1px solid #B9C7D8; border-radius: 15px; padding: 20px;">
                            <div style="color: #2D2C3A; font-weight: 700; font-size: 13px; margin-bottom: 15px;">Related
                                Questions</div>
                            <div style="margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                                    <div style="width: 7px; height: 7px; background: #2ECC71; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">Practice: factorization with mixed
                                        signs</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                                    <div style="width: 7px; height: 7px; background: #2ECC71; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">Similar: solve by completing the
                                        square</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                    <div style="width: 7px; height: 7px; background: #2ECC71; border-radius: 50%;">
                                    </div>
                                    <span style="font-size: 9px; color: #5F6B7A;">Quick check: identify correct
                                        roots</span>
                                </div>
                                <button
                                    style="background: #4766FF; color: white; border: none; padding: 5px 15px; border-radius: 10px; font-size: 9px;">Practice
                                    similar</button>
                            </div>
                        </div>
                    </div>
                    <div
                        style="background: white; border: 1px solid #CACACA; border-radius: 25px; padding: 30px; text-align: center;">
                        <div style="color: #2D2C3A; font-weight: 700; font-size: 13px; margin-bottom: 20px;">Adaptive
                            Practice</div>
                        <div
                            style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div
                                    style="width: 12px; height: 12px; background: #4766FF; border: 2px solid #4766FF; border-radius: 50%;">
                                </div>
                                <span style="font-size: 8px; color: #5F6B7A;">Start</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div
                                    style="width: 12px; height: 12px; background: #4766FF; border: 2px solid #4766FF; border-radius: 50%;">
                                </div>
                                <span style="font-size: 8px; color: #5F6B7A;">Adapting…</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div
                                    style="width: 12px; height: 12px; background: #4766FF; border: 2px solid #4766FF; border-radius: 50%;">
                                </div>
                                <span style="font-size: 8px; color: #5F6B7A;">Consolidate</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div
                                    style="width: 12px; height: 12px; background: white; border: 2px solid #B9C7D8; border-radius: 50%;">
                                </div>
                                <span style="font-size: 8px; color: #5F6B7A;">Mastery</span>
                            </div>
                        </div>
                        <div
                            style="background: #EEF3FF; height: 4px; border-radius: 10px; position: relative; margin-top: 20px;">
                            <div style="background: #4766FF; width: 60%; height: 100%; border-radius: 10px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Teach Section (duplicate removed visually) -->
    <section class="feature-section teach-section" id="teach-duplicate" style="display:none;">
        <div class="section-container">
            <div class="feature-visual animate-fade-in-up">
                <div
                    style="background: linear-gradient(135deg, #4379D8, #F58147, #7BABB1, #D84849, #6A3144); border-radius: 20px; padding: 60px; text-align: center; position: relative; overflow: hidden;">
                    <div
                        style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.1); backdrop-filter: blur(5px);">
                    </div>
                    <div style="position: relative; z-index: 2;">
                        <div style="color: white; font-weight: 600; font-size: 18px; margin-bottom: 30px;">Mathematics
                            Knowledge Map</div>
                        <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 15px;">
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; backdrop-filter: blur(10px);">Algebra</span>
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; backdrop-filter: blur(10px);">Geometry</span>
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 10px; backdrop-filter: blur(10px);">Functions</span>
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 8px; backdrop-filter: blur(10px);">Quadratic
                                Functions</span>
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 6px; backdrop-filter: blur(10px);">Trigonometric
                                Identities</span>
                            <span
                                style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; font-size: 4px; backdrop-filter: blur(10px);">Permutations
                                & Combinations</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="feature-content animate-fade-in-up">
                <h2>Teach</h2>
                <h3 class="feature-subtitle">Turn complexity into clarity</h3>
                <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
            </div>
        </div>
    </section>

    <!-- Track Section -->
    <section class="feature-section track-section" id="track">
        <div class="section-container">
            <!-- Visuals on the left to match the mock -->
            <div class="feature-visual track-visual animate-fade-in-up">
                <div class="track-canvas">
                    <!-- Detections group -->
                    <div class="track-card track-detections">
                        <div class="det-pie">82%<br>Mastered</div>
                        <div class="det-wrapper">
                            <div class="det-card">
                                <div class="det-title"><span class="star"></span> Strengths detection</div>
                                <div class="det-row"><span class="det-bullet" style="background:#32D583"></span><div class="mini-line" style="max-width:180px"></div></div>
                                <div class="det-row"><span class="det-bullet" style="background:#D0D5DD"></span><div class="mini-line" style="max-width:140px; opacity:.45"></div></div>
                            </div>
                            <div class="det-card" style="margin-bottom: 0;">
                                <div class="det-title"><span class="star"></span> Weakness detection</div>
                                <div class="det-row"><span class="det-bullet" style="background:#F04438"></span><div class="mini-line" style="max-width:160px"></div></div>
                                <div class="det-row"><span class="det-bullet" style="background:#D0D5DD"></span><div class="mini-line" style="max-width:120px; opacity:.45"></div></div>
                            </div>
                        </div>
                        <div class="slider-pill"><span class="slider-dot"></span><span class="slider-dot"></span><span class="slider-dot" style="opacity:.5"></span></div>
                    </div>

                    <!-- Chart card -->
                    <div class="track-card track-chart">
                        <div class="chart-header-pill"></div>
                        <div style="display:flex; gap:18px; align-items:center; margin-bottom:14px;">
                            <div class="gauge"></div>
                            <div class="info-lines">
                                <div class="mini-line" style="width:85%"></div>
                                <div class="mini-line" style="width:62%; opacity:.55"></div>
                            </div>
                        </div>
                        <div style="display:flex; gap:20px; align-items:flex-end;">
                            <svg width="220" height="80" viewBox="0 0 220 80" fill="none" xmlns="http://www.w3.org/2000/svg" style="overflow:visible">
                                <path d="M8 62 L50 54 L92 56 L134 46 L176 36 L212 30" stroke="#4C6FFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="8" cy="62" r="5" fill="#4C6FFF"/>
                                <circle cx="50" cy="54" r="5" fill="#4C6FFF"/>
                                <circle cx="92" cy="56" r="5" fill="#4C6FFF"/>
                                <circle cx="134" cy="46" r="5" fill="#4C6FFF"/>
                                <circle cx="176" cy="36" r="5" fill="#4C6FFF"/>
                                <circle cx="212" cy="30" r="5" fill="#4C6FFF"/>
                            </svg>
                            <div class="bars">
                                <div class="bar"></div>
                                <div class="bar"></div>
                                <div class="bar"></div>
                                <div class="bar"></div>
                            </div>
                        </div>
                        <div class="chart-star"></div>
                    </div>
                </div>
            </div>

            <!-- Copy on the right -->
            <div class="feature-content animate-fade-in-up">
                <h2>Track</h2>
                <h3 class="feature-subtitle" style="color:#111827">See your growth</h3>
                <p class="feature-description">Knowledge graphs and insights show progress over time.</p>
            </div>
        </div>
    </section>

    <!-- Study Tool Suite (two-column layout) -->
    <section class="study-tools" id="tools">
        <div class="study-suite">
            <div class="suite-left">
                <h2 class="suite-heading">Study Tool Suite</h2>
                <p class="suite-subtitle">A set of AI tools to make learning <span class="highlight">faster</span> and <span class="highlight">easier</span>.</p>
                <div class="suite-illustration" aria-hidden="true"></div>
            </div>
            <div class="suite-right">
                <div class="suite-list">
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-clipboard"></i></div>
                        <div>
                            <h4>Note-Taking AI</h4>
                            <p>Instantly generate notes and outlines from course materials.</p>
                        </div>
                    </div>
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-file-alt"></i></div>
                        <div>
                            <h4>Exam Simulation AI</h4>
                            <p>Automatically create mock exams and simulate real test environments.</p>
                        </div>
                    </div>
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-circle-xmark"></i></div>
                        <div>
                            <h4>Error Bank AI</h4>
                            <p>Automatically organizes mistakes, supports targeted practice and review.</p>
                        </div>
                    </div>
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-video"></i></div>
                        <div>
                            <h4>Video Explainer AI</h4>
                            <p>Turn knowledge points into short explainer videos or animations.</p>
                        </div>
                    </div>
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-clone"></i></div>
                        <div>
                            <h4>Flashcard AI</h4>
                            <p>One‑click flashcard generation for effective memory and review.</p>
                        </div>
                    </div>
                    <div class="suite-item animate-fade-in-up">
                        <div class="suite-badge"><i class="fas fa-calendar-alt"></i></div>
                        <div>
                            <h4>Study Calendar AI</h4>
                            <p>Intelligently schedules study time, generates study plans and reminders.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section" id="cta">
        <div class="character-bottom"></div>
        <div class="cta-content" style="position: relative; z-index: 2;">
            <h2>Ready to ace the study ?</h2>
            <p class="cta-subtitle">Sign up to revolutionise your learning !</p>
            <a href="/home" target="_top" class="cta-button-large">Start For Free</a>
        </div>
    </section>

    

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-left">
                    <div class="footer-brand">
                        <div class="logo-icon">
                            <div class="logo-squares">
                                <div class="logo-square logo-square-1"></div>
                                <div class="logo-square logo-square-2"></div>
                                <div class="logo-square logo-square-3"></div>
                            </div>
                        </div>
                        <div class="logo-text">Thinky</div>
                    </div>
                    <p class="footer-tagline">Learning Beyond the Answer</p>
                    <div class="social-links">
                        <a href="#" class="social-link">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div class="download-section">
                    <h3>Download</h3>
                    <p class="download-description">Anytime, anywhere, download to use Thinky AI.</p>
                    <div class="download-buttons">
                        <a href="#" class="download-button">
                            <i class="fab fa-apple"></i>
                            <div class="download-info">
                                <div class="download-type">Download</div>
                                <div class="download-store">App Store</div>
                            </div>
                        </a>
                        <a href="#" class="download-button">
                            <i class="fab fa-google-play"></i>
                            <div class="download-info">
                                <div class="download-type">Download</div>
                                <div class="download-store">Google Play</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p class="copyright">© 2025 Thinky AI . All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Feature navigation active state + image swap
        const sections = ['talk', 'think', 'teach', 'test', 'track'];
        const navPills = Array.from(document.querySelectorAll('.nav-pill'));

        function refreshNavImages() {
            navPills.forEach(pill => {
                const img = pill.querySelector('img');
                if (!img) return;
                const normal = pill.getAttribute('data-img');
                const active = pill.getAttribute('data-img-active') || normal;
                img.src = pill.classList.contains('active') ? active : normal;
            });
        }

        window.addEventListener('scroll', function () {
            const scrollPosition = window.scrollY + 200;
            sections.forEach((section, index) => {
                const element = document.getElementById(section);
                if (!element) return;
                const offsetTop = element.offsetTop;
                const offsetBottom = offsetTop + element.offsetHeight;
                if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
                    navPills.forEach(pill => pill.classList.remove('active'));
                    if (navPills[index]) navPills[index].classList.add('active');
                    refreshNavImages();
                }
            });
        });

        // Also refresh once on load
        refreshNavImages();

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function (entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe all sections for animations
        document.querySelectorAll('section, .suite-item, .feature-content, .feature-visual').forEach(el => {
            observer.observe(el);
        });

        // Mobile menu toggle (if needed)
        const navbar = document.querySelector('.navbar');
        let lastScrollTop = 0;

        window.addEventListener('scroll', function () {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up
                navbar.style.transform = 'translateY(0)';
            }
            lastScrollTop = scrollTop;
        });

        // Parallax effect for hero section (skips when `.no-parallax` present)
        window.addEventListener('scroll', function () {
            const scrolled = window.pageYOffset;
            const heroImage = document.querySelector('.hero-image');
            const characterImage = document.querySelector('.character-image');

            if (heroImage && !heroImage.classList.contains('no-parallax') && window.innerWidth > 768) {
                heroImage.style.transform = `translateY(${scrolled * 0.2}px)`;
            }

            if (characterImage && window.innerWidth > 768) {
                characterImage.style.transform = `translateY(${scrolled * 0.1}px)`;
            }
        });
    </script>
</body>

</html>
