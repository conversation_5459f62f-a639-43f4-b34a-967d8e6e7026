import { createRouter, createWebHistory } from 'vue-router'
import Home from '../components/Home.vue'
import Landing from '../components/Landing.vue'
import VoiceTest from '../components/VoiceTest.vue'
import StreamTest from '../components/StreamTest.vue'
import MobileUpload from '../components/mobile/MobileUpload.vue'
import DualScreenStudy from '../components/DualScreenStudy.vue'
import DragTest from '../components/DragTest.vue'

const routes = [
  {
    path: '/',
    name: 'Landing',
    component: Landing
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/dual-screen',
    name: 'DualScreenStudy',
    component: DualScreenStudy
  },
  {
    path: '/m/upload',
    name: 'MobileUpload',
    component: MobileUpload
  },
  {
    path: '/test',
    name: 'VoiceTest',
    component: VoiceTest
  },
  {
    path: '/stream',
    name: 'StreamTest',
    component: StreamTest
  },
  {
    path: '/drag-test',
    name: 'DragTest',
    component: DragTest
  }
]

const router = createRouter({
  // Use Vite base for subpath deployment (defaults to '/')
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
