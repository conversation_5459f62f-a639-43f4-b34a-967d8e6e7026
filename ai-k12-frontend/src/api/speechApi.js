import axios from 'axios'
import { getApiUrl, getWhisperWsUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const speechClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000
})

// WebSocket连接管理器
class WebSocketManager {
  constructor() {
    this.connections = new Map()
  }
  
  /**
   * 获取或创建WebSocket连接
   * @param {string} url - WebSocket URL
   * @param {Object} options - 连接选项
   * @returns {WebSocket} WebSocket实例
   */
  getConnection(url, options = {}) {
    // 生成连接键
    const key = `${url}_${options.sessionId || 'default'}`
    
    // 检查现有连接
    if (this.connections.has(key)) {
      const connection = this.connections.get(key)
      if (connection.readyState === WebSocket.OPEN) {
        return connection
      }
      // 移除无效连接
      this.connections.delete(key)
    }
    
    // 创建新连接
    const ws = new WebSocket(url)
    this.connections.set(key, ws)
    
    // 连接关闭时清理
    ws.addEventListener('close', () => {
      this.connections.delete(key)
    })
    
    return ws
  }
  
  /**
   * 关闭所有连接
   */
  closeAll() {
    for (const [key, ws] of this.connections.entries()) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close(1000, 'Manager cleanup')
      }
      this.connections.delete(key)
    }
  }
  
  /**
   * 关闭特定连接
   * @param {string} url - WebSocket URL
   * @param {string} sessionId - 会话ID
   */
  closeConnection(url, sessionId = 'default') {
    const key = `${url}_${sessionId}`
    if (this.connections.has(key)) {
      const ws = this.connections.get(key)
      if (ws.readyState === WebSocket.OPEN) {
        ws.close(1000, 'Connection cleanup')
      }
      this.connections.delete(key)
    }
  }
}

// 全局WebSocket管理器实例
const wsManager = new WebSocketManager()

// 页面卸载时清理连接
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    wsManager.closeAll()
  })
}

/**
 * 语音识别API - 将音频转换为文字
 * @param {Blob|File} audioBlob - 音频文件
 * @param {string} language - 语言代码，默认为中文
 * @returns {Promise<{text: string, confidence: number}>}
 */
export async function speechToText(audioBlob, language = 'zh-CN') {
  try {
    const formData = new FormData()
    formData.append('audio', audioBlob, 'recording.wav')
    formData.append('language', language)
    
    const response = await speechClient.post('/speech/stt', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('语音识别失败:', error)
    throw new Error('语音识别服务暂时不可用')
  }
}

/**
 * 语音合成API - 将文字转换为语音
 * @param {string} text - 要合成的文字
 * @param {Object} options - 合成选项
 * @param {string} options.voice - 声音类型
 * @param {number} options.speed - 语速 (0.5-2.0)
 * @param {number} options.pitch - 音调 (0.5-2.0)
 * @param {string} options.format - 音频格式 (mp3, wav)
 * @returns {Promise<Blob>} 音频文件Blob
 */
export async function textToSpeech(text, options = {}) {
  try {
    const {
      voice = 'zh-CN-XiaoxiaoNeural',
      speed = 1.0,
      pitch = 1.0,
      format = 'mp3'
    } = options
    
    const response = await speechClient.post('/speech/tts', {
      text,
      voice,
      speed,
      pitch,
      format
    }, {
      responseType: 'blob'
    })
    
    return response.data
  } catch (error) {
    console.error('语音合成失败:', error)
    throw new Error('语音合成服务暂时不可用')
  }
}

/**
 * 获取可用的语音列表
 * @param {string} language - 语言代码
 * @returns {Promise<Array>} 语音列表
 */
export async function getAvailableVoices(language = 'zh-CN') {
  try {
    const response = await speechClient.get('/speech/voices', {
      params: { language }
    })
    
    return response.data
  } catch (error) {
    console.error('获取语音列表失败:', error)
    return []
  }
}

/**
 * 检查语音服务状态
 * @returns {Promise<boolean>} 服务是否可用
 */
export async function checkSpeechServiceStatus() {
  try {
    const response = await speechClient.get('/speech/status')
    return response.data.available || false
  } catch (error) {
    console.error('检查语音服务状态失败:', error)
    return false
  }
}

/**
 * 语音评测API - 用于口语练习评分
 * @param {Blob|File} audioBlob - 音频文件
 * @param {string} referenceText - 参考文本
 * @param {string} language - 语言代码
 * @returns {Promise<Object>} 评测结果
 */
export async function speechEvaluation(audioBlob, referenceText, language = 'zh-CN') {
  try {
    const formData = new FormData()
    formData.append('audio', audioBlob, 'pronunciation.wav')
    formData.append('reference', referenceText)
    formData.append('language', language)
    
    const response = await speechClient.post('/speech/evaluation', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('语音评测失败:', error)
    throw new Error('语音评测服务暂时不可用')
  }
}

// Web Speech API 相关工具函数

/**
 * 检查浏览器是否支持Web Speech API
 * @returns {Object} 支持情况
 */
export function checkWebSpeechSupport() {
  return {
    speechRecognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
    speechSynthesis: 'speechSynthesis' in window
  }
}

/**
 * 检查浏览器是否支持WebSocket
 * @returns {boolean} 是否支持WebSocket
 */
export function checkWebSocketSupport() {
  return typeof WebSocket !== 'undefined'
}

/**
 * 创建WebSocket连接
 * @param {string} url - WebSocket服务器URL
 * @param {Object} options - 连接选项
 * @returns {WebSocket} WebSocket实例
 */
export function createWebSocketConnection(url, options = {}) {
  if (!checkWebSocketSupport()) {
    throw new Error('浏览器不支持WebSocket')
  }
  
  return wsManager.getConnection(url, options)
}

/**
 * 关闭WebSocket连接
 * @param {string} url - WebSocket服务器URL
 * @param {string} sessionId - 会话ID
 */
export function closeWebSocketConnection(url, sessionId) {
  wsManager.closeConnection(url, sessionId)
}

/**
 * 实时语音转录 - 基于WebSocket的流式转录
 * @param {Object} options - 转录选项
 * @param {string} options.wsUrl - WebSocket服务器URL
 * @param {string} options.language - 语言代码
 * @param {Function} options.onTranscription - 转录结果回调
 * @param {Function} options.onStatusChange - 状态变化回调
 * @param {Function} options.onError - 错误回调
 * @returns {Object} 控制对象
 */
export function startRealtimeTranscription(options = {}) {
  const defaultWs = getWhisperWsUrl()
  const {
    wsUrl = defaultWs,
    language = 'zh',
    onTranscription = () => {},
    onStatusChange = () => {},
    onError = () => {}
  } = options
  
  let ws = null
  let isConnected = false
  let isRecording = false
  let sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  try {
    // 创建WebSocket连接
    ws = createWebSocketConnection(wsUrl, { sessionId })
    
    // 连接事件处理
    ws.onopen = () => {
        console.log('[SpeechAPI] WebSocket连接已建立')
      isConnected = true
      onStatusChange('connected')
      
      // 发送配置
      const configMessage = {
        type: 'config',
        language: language,
        task: 'transcribe'
      }
      ws.send(JSON.stringify(configMessage))
    }
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        
        switch (message.type) {
          case 'connected':
            console.log('[SpeechAPI] 服务器确认连接')
            break
            
          case 'config_updated':
            console.log('[SpeechAPI] 配置已更新', message.config)
            break
            
          case 'audio_received':
            console.log('[SpeechAPI] 音频已接收', message.buffer_size)
            break
            
          case 'transcription':
            console.log('[SpeechAPI] 收到转录结果', message.data.text)
            onTranscription(message.data)
            break
            
          case 'error':
            console.error('[SpeechAPI] 服务器错误', message.message)
            onError(new Error(message.message))
            break
            
          case 'pong':
            console.log('[SpeechAPI] 收到心跳响应')
            break
            
          default:
            console.log('[SpeechAPI] 未知消息类型', message)
        }
      } catch (parseError) {
        console.error('[SpeechAPI] 解析消息失败', parseError)
        onError(new Error('解析服务器消息失败'))
      }
    }
    
    ws.onerror = (error) => {
      console.error('[SpeechAPI] WebSocket错误', error)
      onError(new Error('WebSocket连接错误'))
    }
    
    ws.onclose = (event) => {
      console.log('[SpeechAPI] WebSocket连接已关闭', event.code, event.reason)
      isConnected = false
      isRecording = false
      onStatusChange('disconnected')
    }
    
    // 返回控制对象
    return {
      /**
       * 发送音频数据
       * @param {ArrayBuffer|Float32Array} audioData - 音频数据
       * @param {number} sampleRate - 采样率
       */
      sendAudioData(audioData, sampleRate = 16000) {
        if (!isConnected) {
          console.warn('[SpeechAPI] 未连接到服务器，无法发送音频数据')
          return
        }
        
        try {
          // 转换为Float32Array
          let float32Data
          if (audioData instanceof Float32Array) {
            float32Data = audioData
          } else if (audioData instanceof ArrayBuffer) {
            float32Data = new Float32Array(audioData)
          } else {
            throw new Error('不支持的音频数据格式')
          }
          
          // 转换为Base64
          const base64Data = arrayBufferToBase64(float32Data.buffer)
          
          // 发送消息
          const message = {
            type: 'audio_data',
            audio_data: base64Data,
            sample_rate: sampleRate,
            samples: float32Data.length
          }
          
          ws.send(JSON.stringify(message))
          console.log('[SpeechAPI] 发送音频数据', float32Data.length, '样本')
        } catch (error) {
          console.error('[SpeechAPI] 发送音频数据失败', error)
          onError(error)
        }
      },
      
      /**
       * 开始录音状态
       */
      startRecording() {
        isRecording = true
        onStatusChange('recording')
      },
      
      /**
       * 停止录音状态
       */
      stopRecording() {
        isRecording = false
        onStatusChange('connected')
      },
      
      /**
       * 发送心跳
       */
      sendPing() {
        if (isConnected) {
          ws.send(JSON.stringify({ type: 'ping' }))
        }
      },
      
      /**
       * 关闭连接
       */
      close() {
        if (ws && isConnected) {
          ws.close(1000, 'User requested close')
        }
        closeWebSocketConnection(wsUrl, sessionId)
      },
      
      /**
       * 获取当前状态
       */
      getStatus() {
        return {
          isConnected,
          isRecording,
          sessionId
        }
      }
    }
    
  } catch (error) {
    console.error('[SpeechAPI] 创建实时转录失败', error)
    onError(error)
    throw error
  }
}

/**
 * ArrayBuffer转Base64
 * @param {ArrayBuffer} buffer - ArrayBuffer
 * @returns {string} Base64字符串
 */
function arrayBufferToBase64(buffer) {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * 创建浏览器内置语音识别实例
 * @param {Object} options - 配置选项
 * @returns {SpeechRecognition|null}
 */
export function createSpeechRecognition(options = {}) {
  const support = checkWebSpeechSupport()
  if (!support.speechRecognition) {
    console.warn('浏览器不支持语音识别')
    return null
  }
  
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  const recognition = new SpeechRecognition()
  
  // 默认配置
  recognition.continuous = options.continuous || false
  recognition.interimResults = options.interimResults || true
  recognition.lang = options.language || 'zh-CN'
  recognition.maxAlternatives = options.maxAlternatives || 1
  
  return recognition
}

/**
 * 使用浏览器内置TTS播放文本
 * @param {string} text - 要播放的文本
 * @param {Object} options - 播放选项
 * @returns {Promise<void>}
 */
export function speakText(text, options = {}) {
  return new Promise((resolve, reject) => {
    const support = checkWebSpeechSupport()
    if (!support.speechSynthesis) {
      reject(new Error('浏览器不支持语音合成'))
      return
    }
    
    // 停止当前播放
    speechSynthesis.cancel()
    
    const utterance = new SpeechSynthesisUtterance(text)
    
    // 配置选项
    utterance.lang = options.language || 'zh-CN'
    utterance.rate = options.rate || 1.0
    utterance.pitch = options.pitch || 1.0
    utterance.volume = options.volume || 1.0
    
    // 如果指定了声音，尝试使用
    if (options.voice) {
      const voices = speechSynthesis.getVoices()
      const selectedVoice = voices.find(voice => 
        voice.name === options.voice || voice.voiceURI === options.voice
      )
      if (selectedVoice) {
        utterance.voice = selectedVoice
      }
    }
    
    utterance.onend = () => resolve()
    utterance.onerror = (event) => reject(new Error(`语音播放失败: ${event.error}`))
    
    speechSynthesis.speak(utterance)
  })
}

/**
 * 获取浏览器可用的TTS声音列表
 * @returns {Promise<Array>} 声音列表
 */
export function getBrowserVoices() {
  return new Promise((resolve) => {
    const support = checkWebSpeechSupport()
    if (!support.speechSynthesis) {
      resolve([])
      return
    }
    
    let voices = speechSynthesis.getVoices()
    
    if (voices.length === 0) {
      // 某些浏览器需要等待voices加载
      speechSynthesis.onvoiceschanged = () => {
        voices = speechSynthesis.getVoices()
        resolve(voices)
      }
    } else {
      resolve(voices)
    }
  })
}

/**
 * 停止当前TTS播放
 */
export function stopSpeaking() {
  const support = checkWebSpeechSupport()
  if (support.speechSynthesis) {
    speechSynthesis.cancel()
  }
}
