import axios from 'axios'
import { getApiUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => response,
  error => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

/**
 * K12教育聊天API - 流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function k12ChatStream(memoryId, message, subject, grade, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/k12/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message,
    subject: subject || '',
    grade: grade || ''
  })

  const fullUrl = `${url}?${params.toString()}`
  console.log('创建K12聊天流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)
  let isStreamCompleted = false  // 标记流是否已正常完成

  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        console.log('K12聊天流正常结束')
        isStreamCompleted = true  // 标记流已正常完成
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }

  eventSource.onerror = function(event) {
    console.log('K12聊天流连接状态:', eventSource.readyState)

    // 如果流已经正常完成，不要报告错误
    if (isStreamCompleted) {
      console.log('K12聊天流已正常完成，忽略后续错误事件')
      return
    }

    console.error('K12聊天流错误:', event)
    onError(new Error('K12教育服务连接失败'))
    eventSource.close()
  }

  return eventSource
}

/**
 * 普通聊天API - 兼容原有功能
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function chatWithSSE(memoryId, message, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message
  })

  const fullUrl = `${url}?${params.toString()}`
  console.log('创建普通聊天流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)
  let isStreamCompleted = false  // 标记流是否已正常完成

  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        console.log('普通聊天流正常结束')
        isStreamCompleted = true  // 标记流已正常完成
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }

  eventSource.onerror = function(event) {
    console.log('普通聊天流连接状态:', eventSource.readyState)

    // 如果流已经正常完成，不要报告错误
    if (isStreamCompleted) {
      console.log('普通聊天流已正常完成，忽略后续错误事件')
      return
    }

    console.error('普通聊天流错误:', event)
    onError(new Error('聊天服务连接失败'))
    eventSource.close()
  }

  return eventSource
}

/**
 * 多模态聊天接口 - 支持文本+图片（文件或URL）流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 文本消息
 * @param {File|string|null} image - 图片文件或图片URL（可选）
 * @param {string} language - 语言（可选，默认'en'）
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} 一个带有 close 方法的对象
 */
export function multimodalChatStream(memoryId, message, image, language = 'en', onMessage, onError, onClose) {
  // 使用 fetch 发送 multipart/form-data 请求
  const formData = new FormData()
  formData.append('memoryId', memoryId || '')
  formData.append('message', message)
  if (image instanceof File) {
    formData.append('image', image)
  } else if (typeof image === 'string' && image) {
    // 支持直接传入已经在服务器上的图片URL（例如手机扫码上传返回的地址）
    formData.append('imageUrl', image)
  }
  // 添加语言参数
  if (language && language.trim()) {
    formData.append('language', language.trim())
  }
  
  // 由于 EventSource 不支持 POST 请求，我们需要使用 fetch + ReadableStream
  console.log('创建多模态聊天流连接:', `${API_BASE_URL}/ai/chat/multimodal`)
  
  let isStreamCompleted = false
  
  fetch(`${API_BASE_URL}/ai/chat/multimodal`, {
    method: 'POST',
    body: formData
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    const pump = () => reader.read().then(({ done, value }) => {
      if (done) {
        // 处理收尾残留
        if (buffer.trim()) {
          parseBuffer(buffer, onMessage)
        }
        console.log('多模态聊天流正常结束')
        isStreamCompleted = true
        onClose && onClose()
        return
      }

      buffer += decoder.decode(value, { stream: true })
      // 规范化换行
      buffer = buffer.replace(/\r\n/g, '\n')

      // 以空行作为事件分隔符
      const parts = buffer.split(/\n\n/)
      // 最后一个可能是不完整事件，保留在缓冲区
      buffer = parts.pop() || ''
      for (const evt of parts) {
        const data = extractSseData(evt)
        if (data === null) continue
        if (data === '[DONE]') {
          console.log('多模态聊天流结束标识')
          isStreamCompleted = true
          onClose && onClose()
          return
        }
        onMessage && onMessage(data)
      }

      return pump()
    }).catch(error => {
      if (!isStreamCompleted) {
        console.error('多模态聊天流错误:', error)
        onError && onError(error)
      }
    })

    const extractSseData = (eventChunk) => {
      // 一个事件可能包含多行 data:，需要拼接
      const lines = eventChunk.split('\n')
      let dataLines = []
      for (const line of lines) {
        if (line.startsWith('data:')) {
          // 允许 'data:' 或 'data: '
          dataLines.push(line.replace(/^data:\s?/, ''))
        }
      }
      if (dataLines.length === 0) return null
      return dataLines.join('\n').trim()
    }

    const parseBuffer = (buf, onMsg) => {
      const parts = buf.split(/\n\n/)
      for (const evt of parts) {
        const data = extractSseData(evt)
        if (data && data !== '[DONE]') onMsg && onMsg(data)
      }
    }

    pump()
  })
  .catch(error => {
    if (!isStreamCompleted) {
      console.error('多模态聊天请求失败:', error)
      onError && onError(error)
    }
  })
  
  // 返回一个模拟的 EventSource 对象用于取消
  return {
    close: () => {
      isStreamCompleted = true
    }
  }
}

/**
 * 发送图片消息（废弃，使用 multimodalChatStream 替代）
 * @deprecated 使用 multimodalChatStream 替代
 */
export async function sendImageMessage(memoryId, imageFile, description, subject, grade) {
  try {
    const formData = new FormData()
    formData.append('image', imageFile)
    formData.append('memoryId', memoryId || '')
    formData.append('description', description || '')
    formData.append('subject', subject || '')
    formData.append('grade', grade || '')
    
    const response = await apiClient.post('/chat/k12/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('发送图片消息失败:', error)
    throw error
  }
}

/**
 * 获取学习建议
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} topic - 主题
 * @returns {Promise}
 */
export async function getStudySuggestions(subject, grade, topic) {
  try {
    const response = await apiClient.get('/chat/k12/suggestions', {
      params: { subject, grade, topic }
    })
    return response.data
  } catch (error) {
    console.error('获取学习建议失败:', error)
    throw error
  }
}

/**
 * 获取练习题
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} concept - 知识点
 * @returns {Promise}
 */
export async function getPracticeQuestions(subject, grade, concept) {
  try {
    const response = await apiClient.get('/chat/k12/practice', {
      params: { subject, grade, concept }
    })
    return response.data
  } catch (error) {
    console.error('获取练习题失败:', error)
    throw error
  }
}

/**
 * 分析学习进度
 * @param {string} memoryId - 会话ID
 * @returns {Promise}
 */
export async function analyzeProgress(memoryId) {
  try {
    const response = await apiClient.get(`/chat/k12/progress/${memoryId}`)
    return response.data
  } catch (error) {
    console.error('分析学习进度失败:', error)
    throw error
  }
}

/**
 * 普通AI聊天接口（支持数学工具）- 流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} language - 语言（可选，默认'en'）
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function aiChatStream(memoryId, message, language = 'en', onMessage, onError, onClose) {
  // 确保memoryId是有效的数字
  const numericMemoryId = memoryId ? parseInt(memoryId) : Date.now()
  
  const params = new URLSearchParams({
    memoryId: numericMemoryId.toString(),
    message: message
  })
  
  // 添加语言参数
  if (language && language.trim()) {
    params.append('language', language.trim())
  }

  const fullUrl = `${API_BASE_URL}/ai/chat?${params.toString()}`
  console.log('创建AI聊天流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)

  // 设置超时处理（60秒无响应则认为连接异常）
  let timeoutId = null
  let hasReceivedData = false
  let isStreamCompleted = false  // 标记流是否已正常完成

  const resetTimeout = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      if (!hasReceivedData) {
        console.error('AI聊天流式响应超时，未收到任何数据')
        eventSource.close()
        onError && onError(new Error('响应超时，请检查网络连接或稍后重试'))
      }
    }, 60000) // 60秒超时
  }

  resetTimeout()
  
  eventSource.onmessage = function(event) {
    try {
      hasReceivedData = true
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }

      const data = event.data
      console.log(`[AI Chat API] 收到消息事件 - type: ${event.type}, data: [${data}]`)

      // 检查是否是流结束标识
      if (data === '[DONE]' || event.type === 'end') {
        console.log('[AI Chat API] 检测到流结束标识，正常结束流式响应')
        isStreamCompleted = true  // 标记流已正常完成
        eventSource.close()
        onClose && onClose()
        return
      }

      // 检查是否是错误消息
      if (data && data.startsWith('{"error"')) {
        const errorData = JSON.parse(data)
        console.error('[AI Chat API] 服务端错误:', errorData.error)
        onError && onError(new Error(errorData.error))
        eventSource.close()
        return
      }

      // 正常的流式内容
      if (data && data.trim() !== '') {
        console.log(`[AI Chat API] 传递正常数据给组件: [${data.substring(0, 50)}...]`)
        onMessage && onMessage(data)
      } else {
        console.log('[AI Chat API] 忽略空数据')
      }
    } catch (error) {
      console.error('[AI Chat API] 解析消息失败:', error)
      onError && onError(error)
    }
  }
  
  eventSource.onerror = function(error) {
    console.log(`[AI Chat API] 错误事件触发 - readyState: ${eventSource.readyState}, isStreamCompleted: ${isStreamCompleted}`)

    // 清理超时定时器
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }

    // 如果流已经正常完成，不要报告错误
    if (isStreamCompleted) {
      console.log('[AI Chat API] 流已正常完成，忽略后续错误事件')
      return
    }

    // 只有在连接未正常关闭时才报告错误
    if (eventSource.readyState === EventSource.CONNECTING) {
      console.error('[AI Chat API] 连接失败:', error)
      onError && onError(new Error('无法连接到AI聊天服务'))
    } else if (eventSource.readyState === EventSource.OPEN) {
      console.error('[AI Chat API] 连接中断:', error)
      onError && onError(new Error('AI聊天服务连接中断'))
    } else {
      console.log('[AI Chat API] 连接正常关闭')
    }

    // 确保连接被关闭
    if (eventSource.readyState !== EventSource.CLOSED) {
      eventSource.close()
    }
  }

  // 处理连接关闭
  eventSource.addEventListener('close', function() {
    console.log('AI Chat SSE 连接已关闭')
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    onClose && onClose()
  })

  // 返回EventSource实例，同时提供清理方法
  const originalClose = eventSource.close.bind(eventSource)
  eventSource.close = function() {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    originalClose()
  }

  return eventSource
}

/**
 * OpenAI兼容TTS语音合成API
 * @param {string} text - 要合成语音的文本
 * @param {Object} options - 合成选项
 * @param {string} options.model - TTS模型 (默认: 'tts-1')
 * @param {string} options.voice - 语音类型 (默认: 'alloy')
 * @param {number} options.speed - 语速 (默认: 1.0)
 * @param {string} options.responseFormat - 输出格式 (默认: 'mp3')
 * @returns {Promise<Blob>} 音频数据Blob
 */
export async function openAiTTS(text, options = {}) {
  const {
    model = 'tts-1',
    voice = 'alloy',
    speed = 1.0,
    responseFormat = 'mp3'
  } = options

  console.log('调用OpenAI TTS API:', { text: text.substring(0, 50) + '...', model, voice, speed, responseFormat })

  try {
    const response = await fetch(`${API_BASE_URL}/ai/openai/tts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        input: text,
        voice,
        speed,
        responseFormat
      })
    })

    if (!response.ok) {
      // 尝试解析错误信息
      let errorMessage = `HTTP ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.message || errorMessage
      } catch {
        errorMessage = await response.text() || errorMessage
      }
      throw new Error(`OpenAI TTS API调用失败: ${errorMessage}`)
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.startsWith('audio/')) {
      throw new Error(`意外的响应类型: ${contentType}`)
    }

    const audioBlob = await response.blob()
    console.log('OpenAI TTS成功:', { size: audioBlob.size, type: audioBlob.type })
    
    return audioBlob
  } catch (error) {
    console.error('OpenAI TTS API调用失败:', error)
    throw error
  }
}

/**
 * OpenAI兼容TTS - SSE流式模式
 * 说明：该方法通过POST触发后端的 `/ai/openai/tts/stream`，解析SSE事件并把音频分片（Uint8Array）逐个回调。
 * 注意：浏览器需要使用 MediaSource 才能边下边播（建议mp3）。
 * @param {string} text - 文本
 * @param {Object} options - { model, voice, speed, responseFormat }
 * @param {Object} handlers - { onChunk, onDone, onError }
 * @returns {{ close: Function }} 可取消的对象
 */
export function openAiTTSStream(text, options = {}, handlers = {}) {
  const {
    model = 'tts-1',
    voice = 'alloy',
    speed = 1.0,
    responseFormat = 'mp3',
    streamFormat = 'sse'
  } = options

  const { onChunk, onDone, onError } = handlers
  let isClosed = false

  const controller = new AbortController()

  const toUint8 = (b64) => {
    try {
      // 兼容 data URL 前缀
      if (b64.startsWith('data:')) {
        const idx = b64.indexOf(',')
        if (idx !== -1) b64 = b64.slice(idx + 1)
      }
      const binary = atob(b64)
      const len = binary.length
      const bytes = new Uint8Array(len)
      for (let i = 0; i < len; i++) bytes[i] = binary.charCodeAt(i)
      return bytes
    } catch (e) {
      console.error('[openAiTTSStream] base64解码失败:', e)
      return null
    }
  }

  const extractSseData = (eventChunk) => {
    const lines = eventChunk.split('\n')
    let dataLines = []
    for (const line of lines) {
      if (line.startsWith('data:')) dataLines.push(line.replace(/^data:\s?/, ''))
    }
    if (dataLines.length === 0) return null
    return dataLines.join('\n').trim()
  }

  const parseDataToChunk = (data) => {
    if (!data) return { type: 'unknown' }
    if (data === '[DONE]') return { type: 'done' }
    try {
      const obj = JSON.parse(data)
      // 常见字段名称兼容：
      // - 直接是 base64: audio / audioContent / data
      // - 嵌套 chunk 对象: { chunk: { b64 | data | bytes } }
      // - audio 对象: { audio: { b64 | data } }
      let b64 = null
      if (typeof obj.audio === 'string') b64 = obj.audio
      if (!b64 && typeof obj.audioContent === 'string') b64 = obj.audioContent
      if (!b64 && typeof obj.data === 'string') b64 = obj.data
      if (!b64 && obj.chunk && typeof obj.chunk === 'object') {
        if (typeof obj.chunk.b64 === 'string') b64 = obj.chunk.b64
        else if (typeof obj.chunk.data === 'string') b64 = obj.chunk.data
        else if (typeof obj.chunk.bytes === 'string') b64 = obj.chunk.bytes
      }
      if (!b64 && obj.audio && typeof obj.audio === 'object') {
        if (typeof obj.audio.b64 === 'string') b64 = obj.audio.b64
        else if (typeof obj.audio.data === 'string') b64 = obj.audio.data
      }
      if (typeof b64 === 'string') {
        const bytes = toUint8(b64)
        if (bytes) return { type: 'chunk', bytes }
      }
      // 可能是状态事件
      if (obj.event === 'done') return { type: 'done' }
      return { type: 'meta', payload: obj }
    } catch (_) {
      // 非JSON，尝试作为裸base64处理
      const possible = data.replace(/\s+/g, '')
      if (/^[A-Za-z0-9+/=]+$/.test(possible)) {
        const bytes = toUint8(possible)
        if (bytes) return { type: 'chunk', bytes }
      }
      return { type: 'text', payload: data }
    }
  }

  ;(async () => {
    try {
      const resp = await fetch(`${API_BASE_URL}/ai/openai/tts/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
          model,
          input: text,
          voice,
          speed,
          responseFormat,
          stream: true,
          streamFormat
        }),
        signal: controller.signal
      })
      if (!resp.ok) throw new Error(`HTTP ${resp.status}: ${resp.statusText}`)

      const reader = resp.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      while (true) {
        const { value, done } = await reader.read()
        if (done) break
        buffer += decoder.decode(value, { stream: true })
        buffer = buffer.replace(/\r\n/g, '\n')

        let idx
        while ((idx = buffer.indexOf('\n\n')) !== -1) {
          const rawEvent = buffer.slice(0, idx)
          buffer = buffer.slice(idx + 2)
          const data = extractSseData(rawEvent)
          if (data == null) continue
          const parsed = parseDataToChunk(data)
          if (parsed.type === 'chunk' && onChunk) onChunk(parsed.bytes)
          else if (parsed.type === 'done') {
            if (onDone) onDone()
            return
          }
        }
      }
      // 流自然结束
      if (onDone) onDone()
    } catch (e) {
      if (!isClosed) {
        console.error('[openAiTTSStream] 流式TTS错误:', e)
        onError && onError(e)
      }
    }
  })()

  return {
    close() {
      isClosed = true
      try { controller.abort() } catch (_) {}
    }
  }
}

/**
 * 测试OpenAI TTS服务连接
 * @returns {Promise<Object>} 连接测试结果
 */
export async function testOpenAiTTS() {
  try {
    const response = await apiClient.get('/ai/openai/tts/test')
    return response.data
  } catch (error) {
    console.error('OpenAI TTS连接测试失败:', error)
    throw error
  }
}

/**
 * 获取OpenAI TTS服务状态
 * @returns {Promise<Object>} 服务状态信息
 */
export async function getOpenAiTTSStatus() {
  try {
    const response = await apiClient.get('/ai/openai/tts/status')
    return response.data
  } catch (error) {
    console.error('获取OpenAI TTS状态失败:', error)
    throw error
  }
}
