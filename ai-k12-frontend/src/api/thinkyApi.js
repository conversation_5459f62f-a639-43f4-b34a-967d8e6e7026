import { getApiUrl } from '../config/env.js'

/**
 * ThinkyAI API - 用于调用后端的 ThinkyAI 引导式学习接口
 */

/**
 * ThinkyAI 流式聊天接口 - 支持结构化输出
 * @param {string} sessionId 会话ID
 * @param {string} message 用户消息
 * @param {string} subject 学科（可选）
 * @param {string} problem 问题类型（可选）
 * @param {string} language 语言，默认'zh'
 * @param {function} onChunk 处理流式数据回调
 * @param {function} onError 错误处理回调
 * @param {function} onComplete 完成处理回调
 * @returns {EventSource} 事件源对象
 */
export function thinkyAiChatStream(sessionId, message, subject, problem, language = 'zh', onChunk, onError, onComplete) {
  try {
    const apiUrl = getApiUrl()
    const params = new URLSearchParams()
    params.append('sessionId', sessionId)
    params.append('message', message)
    params.append('language', language)
    
    if (subject) {
      params.append('subject', subject)
    }
    if (problem) {
      params.append('problem', problem)
    }

    const url = `${apiUrl}/ai/thinky/chat/stream?${params.toString()}`
    console.log(`[ThinkyAPI] 连接流式接口: ${url}`)

    const eventSource = new EventSource(url)
    
    // 处理普通消息事件
    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        console.log(`[ThinkyAPI] 收到响应:`, data)
        
        // 处理流式内容
        if (data.streamContent) {
          onChunk(data.streamContent)
        }
        
        // 检查是否包含学习状态
        if (data.learningStatus) {
          console.log(`[ThinkyAPI] 收到学习状态:`, data.learningStatus)
          // 将学习状态也作为特殊数据传递
          onChunk(JSON.stringify({
            type: 'learning_status',
            learningStatus: data.learningStatus
          }))
        }
      } catch (err) {
        console.warn(`[ThinkyAPI] 解析响应失败:`, err, event.data)
        // 如果解析失败，直接当作文本处理
        onChunk(event.data)
      }
    }

    // 处理结构化输出事件
    eventSource.addEventListener('structured', function(event) {
      try {
        const data = JSON.parse(event.data)
        console.log(`[ThinkyAPI] 收到结构化输出:`, data)
        
        if (data.structuredData) {
          // 使用特殊的标记来区分结构化数据
          onChunk(JSON.stringify({
            type: 'structured',
            structuredType: data.structuredType,
            structuredData: data.structuredData,
            learningStatus: data.learningStatus
          }))
        }
      } catch (err) {
        console.error(`[ThinkyAPI] 解析结构化输出失败:`, err, event.data)
      }
    })

    eventSource.onerror = function(event) {
      console.error(`[ThinkyAPI] 连接错误:`, event)
      eventSource.close()
      if (onError) {
        onError(new Error('ThinkyAI 流式连接失败'))
      }
    }

    // 检测连接关闭
    eventSource.addEventListener('close', function() {
      console.log(`[ThinkyAPI] 连接已关闭`)
      eventSource.close()
      if (onComplete) {
        onComplete()
      }
    })

    // 在一定时间后如果没有收到数据就关闭连接
    setTimeout(() => {
      if (eventSource.readyState === EventSource.OPEN) {
        console.log(`[ThinkyAPI] 流式响应完成，关闭连接`)
        eventSource.close()
        if (onComplete) {
          onComplete()
        }
      }
    }, 60000) // 60秒超时

    return eventSource
    
  } catch (error) {
    console.error('[ThinkyAPI] 创建流式连接失败:', error)
    if (onError) {
      onError(error)
    }
    return null
  }
}

/**
 * ThinkyAI 同步聊天接口
 * @param {string} sessionId 会话ID
 * @param {string} message 用户消息
 * @param {string} subject 学科（可选）
 * @param {string} problem 问题类型（可选）
 * @param {string} language 语言，默认'zh'
 * @returns {Promise} 返回聊天响应
 */
export async function thinkyAiChat(sessionId, message, subject, problem, language = 'zh') {
  try {
    const apiUrl = getApiUrl()
    const url = `${apiUrl}/ai/thinky/chat`
    
    const requestBody = {
      sessionId,
      message,
      language
    }
    
    if (subject) {
      requestBody.subject = subject
    }
    if (problem) {
      requestBody.problem = problem
    }

    console.log(`[ThinkyAPI] 发送同步请求:`, requestBody)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      throw new Error(`ThinkyAI API 错误: ${response.status} - ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`[ThinkyAPI] 收到同步响应:`, data)
    return data

  } catch (error) {
    console.error('[ThinkyAPI] 同步聊天失败:', error)
    throw error
  }
}

/**
 * 获取会话学习状态
 * @param {string} sessionId 会话ID
 * @returns {Promise} 返回学习状态
 */
export async function getThinkySessionStatus(sessionId) {
  try {
    const apiUrl = getApiUrl()
    const url = `${apiUrl}/ai/thinky/session/${sessionId}/status`
    
    console.log(`[ThinkyAPI] 获取会话状态: ${sessionId}`)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      if (response.status === 404) {
        return null // 会话不存在
      }
      throw new Error(`获取会话状态失败: ${response.status} - ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`[ThinkyAPI] 会话状态:`, data)
    return data

  } catch (error) {
    console.error('[ThinkyAPI] 获取会话状态失败:', error)
    throw error
  }
}

/**
 * 重置会话状态
 * @param {string} sessionId 会话ID
 * @returns {Promise} 重置结果
 */
export async function resetThinkySession(sessionId) {
  try {
    const apiUrl = getApiUrl()
    const url = `${apiUrl}/ai/thinky/session/${sessionId}/reset`
    
    console.log(`[ThinkyAPI] 重置会话状态: ${sessionId}`)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`重置会话状态失败: ${response.status} - ${response.statusText}`)
    }

    console.log(`[ThinkyAPI] 会话状态已重置: ${sessionId}`)
    return true

  } catch (error) {
    console.error('[ThinkyAPI] 重置会话状态失败:', error)
    throw error
  }
}

/**
 * 记录学习事件
 * @param {string} sessionId 会话ID
 * @param {object} event 学习事件 {eventType: 'mistake'|'success'|'mastery', description, concept, score}
 * @returns {Promise} 记录结果
 */
export async function recordThinkyLearningEvent(sessionId, event) {
  try {
    const apiUrl = getApiUrl()
    const url = `${apiUrl}/ai/thinky/session/${sessionId}/event`
    
    console.log(`[ThinkyAPI] 记录学习事件:`, event)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(event)
    })

    if (!response.ok) {
      throw new Error(`记录学习事件失败: ${response.status} - ${response.statusText}`)
    }

    console.log(`[ThinkyAPI] 学习事件已记录`)
    return true

  } catch (error) {
    console.error('[ThinkyAPI] 记录学习事件失败:', error)
    throw error
  }
}
