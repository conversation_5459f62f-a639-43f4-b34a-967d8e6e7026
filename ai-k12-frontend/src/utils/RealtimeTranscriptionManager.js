/**
 * 实时转录管理器
 * 提供更高级的转录管理功能，包括历史记录、状态管理等
 */
import RealTimeWhisperClient from './RealTimeWhisperClient.js'
import { getWhisperWsUrl } from '../config/env.js'

class RealtimeTranscriptionManager {
  constructor(options = {}) {
    const defaultWs = getWhisperWsUrl()
    this.options = {
      wsUrl: defaultWs,
      language: 'zh',
      maxReconnectAttempts: 5,
      reconnectDelay: 2000,
      debug: false,
      ...options
    }
    
    // 回调函数
    this.onTranscription = options.onTranscription || (() => {})
    this.onStatusChange = options.onStatusChange || (() => {})
    this.onError = options.onError || (() => {})
    
    // 客户端实例
    this.client = null
    
    // 状态管理
    this.isActive = false
    this.currentStatus = 'stopped'
    
    // 转录历史
    this.transcriptionHistory = []
    this.maxHistorySize = options.maxHistorySize || 100
    
    // 性能监控
    this.metrics = {
      totalTranscriptions: 0,
      connectionDrops: 0,
      averageLatency: 0,
      latencyHistory: []
    }
    
    // 会话管理
    this.sessionId = this.generateSessionId()
    this.sessionStartTime = null
  }
  
  /**
   * 启动实时转录
   */
  async start() {
    if (this.isActive) {
      console.warn('[RealtimeTranscriptionManager] 转录已在运行中')
      return
    }
    
    try {
      // 创建客户端实例
      this.client = new RealTimeWhisperClient({
        ...this.options,
        onTranscription: (result) => this.handleTranscription(result),
        onStatusChange: (status) => this.handleStatusChange(status),
        onError: (error) => this.handleError(error)
      })
      
      // 连接并开始录音
      await this.client.connect()
      await this.client.startRecording()
      
      this.isActive = true
      this.sessionStartTime = new Date()
      this.updateStatus('running')
      
      console.log('[RealtimeTranscriptionManager] 实时转录已启动')
      
    } catch (error) {
      console.error('[RealtimeTranscriptionManager] 启动失败:', error)
      this.handleError(error)
      throw error
    }
  }
  
  /**
   * 停止实时转录
   */
  async stop() {
    if (!this.isActive) {
      return
    }
    
    try {
      if (this.client) {
        this.client.stopRecording()
        this.client.disconnect()
        this.client = null
      }
      
      this.isActive = false
      this.updateStatus('stopped')
      
      console.log('[RealtimeTranscriptionManager] 实时转录已停止')
      
    } catch (error) {
      console.error('[RealtimeTranscriptionManager] 停止失败:', error)
      this.handleError(error)
    }
  }
  
  /**
   * 暂停转录（保持连接）
   */
  pause() {
    if (this.client && this.client.isRecording) {
      this.client.stopRecording()
      this.updateStatus('paused')
    }
  }
  
  /**
   * 恢复转录
   */
  async resume() {
    if (this.client && !this.client.isRecording) {
      await this.client.startRecording()
      this.updateStatus('running')
    }
  }
  
  /**
   * 重启转录服务
   */
  async restart() {
    await this.stop()
    await new Promise(resolve => setTimeout(resolve, 1000))
    await this.start()
  }
  
  /**
   * 处理转录结果
   */
  handleTranscription(result) {
    const transcriptionRecord = {
      id: this.generateTranscriptionId(),
      sessionId: this.sessionId,
      timestamp: new Date(),
      text: result.text,
      language: result.language,
      confidence: result.language_probability,
      duration: result.duration,
      segments: result.segments || []
    }
    
    // 添加到历史记录
    this.addToHistory(transcriptionRecord)
    
    // 更新性能指标
    this.updateMetrics(transcriptionRecord)
    
    // 调用回调
    this.onTranscription(transcriptionRecord)
    
    console.log('[RealtimeTranscriptionManager] 转录结果:', transcriptionRecord.text)
  }
  
  /**
   * 处理状态变化
   */
  handleStatusChange(status) {
    const statusMap = {
      'connected': 'connected',
      'recording': 'running',
      'reconnecting': 'reconnecting',
      'disconnected': 'stopped'
    }
    
    const mappedStatus = statusMap[status] || status
    this.updateStatus(mappedStatus)
    
    // 记录连接断开
    if (status === 'disconnected' && this.isActive) {
      this.metrics.connectionDrops++
    }
  }
  
  /**
   * 处理错误
   */
  handleError(error) {
    console.error('[RealtimeTranscriptionManager] 错误:', error)
    
    const errorRecord = {
      timestamp: new Date(),
      sessionId: this.sessionId,
      error: error.message || error.toString(),
      status: this.currentStatus
    }
    
    this.onError(errorRecord)
  }
  
  /**
   * 更新状态
   */
  updateStatus(newStatus) {
    if (this.currentStatus !== newStatus) {
      const oldStatus = this.currentStatus
      this.currentStatus = newStatus
      
      console.log(`[RealtimeTranscriptionManager] 状态变更: ${oldStatus} -> ${newStatus}`)
      this.onStatusChange(newStatus, oldStatus)
    }
  }
  
  /**
   * 添加到历史记录
   */
  addToHistory(record) {
    this.transcriptionHistory.unshift(record)
    
    // 限制历史记录大小
    if (this.transcriptionHistory.length > this.maxHistorySize) {
      this.transcriptionHistory = this.transcriptionHistory.slice(0, this.maxHistorySize)
    }
  }
  
  /**
   * 更新性能指标
   */
  updateMetrics(record) {
    this.metrics.totalTranscriptions++
    
    // 计算延迟（简化版本）
    const latency = Date.now() - record.timestamp.getTime()
    this.metrics.latencyHistory.push(latency)
    
    // 保持延迟历史大小
    if (this.metrics.latencyHistory.length > 50) {
      this.metrics.latencyHistory.shift()
    }
    
    // 计算平均延迟
    this.metrics.averageLatency = this.metrics.latencyHistory.reduce((a, b) => a + b, 0) / this.metrics.latencyHistory.length
  }
  
  /**
   * 获取转录历史
   */
  getTranscriptionHistory(limit = null) {
    if (limit) {
      return this.transcriptionHistory.slice(0, limit)
    }
    return [...this.transcriptionHistory]
  }
  
  /**
   * 清空转录历史
   */
  clearHistory() {
    this.transcriptionHistory = []
    console.log('[RealtimeTranscriptionManager] 历史记录已清空')
  }
  
  /**
   * 搜索转录历史
   */
  searchHistory(query, options = {}) {
    const {
      caseSensitive = false,
      maxResults = 10,
      dateFrom = null,
      dateTo = null
    } = options
    
    let results = this.transcriptionHistory.filter(record => {
      // 文本匹配
      const text = caseSensitive ? record.text : record.text.toLowerCase()
      const searchQuery = caseSensitive ? query : query.toLowerCase()
      const textMatch = text.includes(searchQuery)
      
      // 日期过滤
      let dateMatch = true
      if (dateFrom) {
        dateMatch = dateMatch && record.timestamp >= dateFrom
      }
      if (dateTo) {
        dateMatch = dateMatch && record.timestamp <= dateTo
      }
      
      return textMatch && dateMatch
    })
    
    return results.slice(0, maxResults)
  }
  
  /**
   * 导出转录历史
   */
  exportHistory(format = 'json') {
    const data = {
      sessionId: this.sessionId,
      exportTime: new Date(),
      totalRecords: this.transcriptionHistory.length,
      records: this.transcriptionHistory
    }
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2)
        
      case 'csv':
        const headers = ['时间戳', '文本', '语言', '置信度', '时长']
        const rows = this.transcriptionHistory.map(record => [
          record.timestamp.toISOString(),
          `"${record.text.replace(/"/g, '""')}"`,
          record.language,
          record.confidence,
          record.duration
        ])
        return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
        
      case 'txt':
        return this.transcriptionHistory
          .map(record => `[${record.timestamp.toLocaleString()}] ${record.text}`)
          .join('\n')
          
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }
  }
  
  /**
   * 获取性能指标
   */
  getMetrics() {
    return {
      ...this.metrics,
      sessionDuration: this.sessionStartTime ? Date.now() - this.sessionStartTime.getTime() : 0,
      isActive: this.isActive,
      currentStatus: this.currentStatus,
      historySize: this.transcriptionHistory.length
    }
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isActive: this.isActive,
      status: this.currentStatus,
      sessionId: this.sessionId,
      sessionStartTime: this.sessionStartTime,
      clientStatus: this.client ? this.client.getStatus() : null,
      metrics: this.getMetrics()
    }
  }
  
  /**
   * 设置语言
   */
  setLanguage(language) {
    this.options.language = language
    if (this.client) {
      this.client.language = language
      this.client.sendConfig()
    }
  }
  
  /**
   * 生成会话ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 生成转录ID
   */
  generateTranscriptionId() {
    return `trans_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 销毁管理器
   */
  destroy() {
    this.stop()
    this.clearHistory()
    this.client = null
    console.log('[RealtimeTranscriptionManager] 管理器已销毁')
  }
}

export default RealtimeTranscriptionManager
