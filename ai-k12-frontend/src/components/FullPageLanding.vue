<template>
  <div id="fullpage">
    <!-- Section 1: Hero -->
    <div class="section hero-section">
      <div class="hero-overlay">
        <h1 class="hero-overlay-title">Beyond the Answer, Towards Thinking</h1>
        <p class="hero-overlay-subtitle">An AI tutor that teaches how to think, not just answer.</p>
      </div>
      <img src="/images/learner.png" alt="Learner" class="hero-learner" />
      <img src="/images/hero.png" alt="Hero" class="hero-full" />

      <!-- Philosophy Banner -->
      <div class="hero-philosophy">
        <h2 class="hero-philosophy-title">Thinky Learning Philosophy</h2>
        <p class="hero-philosophy-subtitle">Answers are not the destination, thinking is the true beginning</p>
      </div>
    </div>

    <!-- Section 2: Talk -->
    <div class="section talk-section">
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up">
          <img class="talk-prototype-image" src="/images/thinky-talk-prototype2.png" alt="Talk section prototype" />
        </div>
        <div class="feature-content animate-fade-in-up">
          <h2>Talk</h2>
          <h3 class="feature-subtitle">Learn through dialogue</h3>
          <p class="feature-description">Smooth conversations that feel like a real teacher guiding you.</p>
        </div>
      </div>
    </div>

    <!-- Section 3: Think -->
    <div class="section think-section">
      <div class="section-container">
        <div class="feature-content animate-fade-in-up">
          <h2>Think</h2>
          <h3 class="feature-subtitle">Think before the answer</h3>
          <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
        </div>
        <div class="feature-visual animate-fade-in-up">
          <img class="think-prototype-image" src="/images/thinky-think-prototype.png" alt="Think section prototype" />
        </div>
      </div>
    </div>

    <!-- Section 4: Teach -->
    <div class="section teach-section">
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up">
          <div class="teach-graph-visual"></div>
        </div>
        <div class="feature-content animate-fade-in-up">
          <h2>Teach</h2>
          <h3 class="feature-subtitle">Turn complexity into clarity</h3>
          <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
        </div>
      </div>
    </div>

    <!-- Section 5: Test -->
    <div class="section test-section">
      <div class="section-container">
        <div class="feature-content animate-fade-in-up">
          <h2>Test</h2>
          <h3 class="feature-subtitle">Spot weaknesses fast</h3>
          <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
        </div>
        <div class="feature-visual animate-fade-in-up">
          <img class="test-prototype-image" src="/images/thinky-test-prototype.png" alt="Test section prototype" />
        </div>
      </div>
    </div>

    <!-- Section 6: Track -->
    <div class="section track-section">
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up">
          <img class="track-prototype-image" src="/images/thinky-track-prototype.png" alt="Track section prototype" />
        </div>
        <div class="feature-content animate-fade-in-up">
          <h2>Track</h2>
          <h3 class="feature-subtitle" style="color:#111827">See your growth</h3>
          <p class="feature-description">Knowledge graphs and insights show progress over time.</p>
        </div>
      </div>
    </div>

    <!-- Section 7: Study Tools -->
    <div class="section tools-section">
      <div class="study-suite">
        <div class="suite-left">
          <h2 class="suite-heading">Study Tool Suite</h2>
          <p class="suite-subtitle">A set of AI tools to make learning <span class="highlight">faster</span> and <span class="highlight">easier</span>.</p>
          <img class="suite-illustration" src="/images/StudyToolSuite.png" alt="Study Tool Suite illustration" />
        </div>
        <div class="suite-right">
          <div class="suite-list">
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-clipboard"></i></div>
              <div>
                <h4>Note-Taking AI</h4>
                <p>Instantly generate notes and outlines from course materials.</p>
              </div>
            </div>
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-file-alt"></i></div>
              <div>
                <h4>Exam Simulation AI</h4>
                <p>Automatically create mock exams and simulate real test environments.</p>
              </div>
            </div>
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-circle-xmark"></i></div>
              <div>
                <h4>Error Bank AI</h4>
                <p>Automatically organizes mistakes, supports targeted practice and review.</p>
              </div>
            </div>
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-video"></i></div>
              <div>
                <h4>Video Explainer AI</h4>
                <p>Turn knowledge points into short explainer videos or animations.</p>
              </div>
            </div>
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-clone"></i></div>
              <div>
                <h4>Flashcard AI</h4>
                <p>One‑click flashcard generation for effective memory and review.</p>
              </div>
            </div>
            <div class="suite-item animate-fade-in-up">
              <div class="suite-badge"><i class="fas fa-calendar-alt"></i></div>
              <div>
                <h4>Study Calendar AI</h4>
                <p>Intelligently schedules study time, generates study plans and reminders.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section 8: CTA -->
    <div class="section cta-section">
      <div class="cta-container">
        <div class="cta-left">
          <h2>Ready to ace the study ?</h2>
          <p class="cta-subtitle">Sign up to revolutionise your learning !</p>
          <a href="javascript:void(0)" @click.prevent="goHome" class="cta-button-large">Start For Free</a>
        </div>
        <div class="cta-right">
          <div class="character-bottom" />
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="fp-nav">
    <div class="nav-brand">
      <img src="/images/logo1.png" alt="Thinky Logo" class="logo-image" />
    </div>
  </nav>
</template>

<script>
import fullpage from 'fullpage.js'
import 'fullpage.js/dist/fullpage.min.css'

export default {
  name: 'FullPageLanding',
  mounted() {
    this.$nextTick(() => {
      this.initFullPage()
    })
  },
  methods: {
    initFullPage() {
      console.log('Initializing fullPage.js...')
      const fp = new fullpage('#fullpage', {
        navigation: true,
        navigationPosition: 'right',
        navigationTooltips: ['Hero', 'Talk', 'Think', 'Teach', 'Test', 'Track', 'Tools', 'Get Started'],
        showActiveTooltip: true,
        scrollingSpeed: 700,
        autoScrolling: true,
        keyboardScrolling: true,
        verticalCentered: true,
        sectionsColor: ['#ffffff', '#ffffff', '#E3E9FA', '#ffffff', '#3F51B5', '#ffffff', '#E3E9FA', '#1e3a8a'],
        sectionSelector: '.section',
        credits: { enabled: false },
        onLeave: function(origin, destination, direction) {
          console.log('Leaving section:', origin.index, 'to:', destination.index)
          const section = origin.item
          section.classList.add('fp-section-leaving')
        },
        afterLoad: function(origin, destination, direction) {
          console.log('Loaded section:', destination.index)
          const section = destination.item
          section.classList.add('fp-section-active')
          const animatedElements = section.querySelectorAll('.feature-text, .feature-visual, .hero-text, .hero-visual')
          animatedElements.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add('animate-in')
            }, index * 200)
          })
        }
      })
      console.log('fullPage.js initialized:', fp)
    },
    goHome() {
      this.$router.push('/home')
    }
  },
  beforeUnmount() {
    if (window.fullpage_api) {
      window.fullpage_api.destroy('all')
    }
  }
}
</script>

<style scoped>
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 基础样式 */
html, body {
  height: 100%;
  width: 100%;
}

#fullpage {
  height: 100vh;
  width: 100vw;
}

.fp-nav {
  position: fixed !important;
  top: 20px !important;
  left: 20px !important;
  right: auto !important;
  z-index: 1000 !important;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 48px;
  width: auto;
  object-fit: contain;
}

#fp-nav ul li a span {
  background: rgba(255, 255, 255, 0.5) !important;
  width: 8px !important;
  height: 8px !important;
}

#fp-nav ul li a.active span {
  background: #3F51B5 !important;
  width: 12px !important;
  height: 12px !important;
}

#fp-nav ul li .fp-tooltip {
  background: #3F51B5 !important;
  color: white !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
}

.section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  position: relative;
}

/* Hero Section - 完全按照原始 Landing.vue */
.hero-section {
  position: relative;
  background: #ffffff;
  overflow: hidden;
  min-height: auto;
}

.hero-full {
  display: block;
  width: 100%;
  height: auto;
}

.hero-learner {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 180px;
  width: 420px;
  height: auto;
  z-index: 2;
  pointer-events: none;
}

.hero-overlay {
  position: absolute;
  left: 0;
  right: 0;
  top: 100px;
  text-align: center;
  z-index: 3;
  pointer-events: none;
  padding: 0 20px;
}

.hero-overlay-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: clamp(32px, 4vw, 56px);
  color: #33439B;
  margin-bottom: 16px;
}

.hero-overlay-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: clamp(16px, 2.2vw, 28px);
  color: #33439B;
  opacity: 0.95;
}

/* Philosophy banner under hero */
.hero-philosophy {
  color: #FFFFFF;
  text-align: center;
  padding: 40px 20px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  pointer-events: none;
}

.hero-philosophy-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(32px, 4vw, 48px);
  line-height: 1.2;
  margin-bottom: 12px;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-philosophy-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(16px, 2vw, 20px);
  opacity: 0.95;
  line-height: 1.4;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Shared feature section layout */
.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;
}

.feature-content h2 {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: clamp(60px, 8vw, 118.7px);
  color: #535353;
  margin-bottom: 30px;
  line-height: 1;
}

.feature-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: clamp(24px, 3vw, 34.06px);
  color: #000;
  margin-bottom: 20px;
}

.feature-description {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: clamp(16px, 2vw, 25.54px);
  color: #535353;
  line-height: 1.43;
}

.feature-visual {
  position: relative;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Talk section */
.talk-section {
  background: #FFFFFF;
}

.talk-prototype-image {
  display: block;
  width: 100%;
  height: auto;
}

/* Think section */
.think-section {
  background: #E3E9FA;
  position: relative;
}

.think-section h2 {
  color: #535353;
}

.think-section .feature-subtitle,
.think-section .feature-description {
  color: #2F3348;
}

.think-prototype-image {
  display: block;
  width: 100%;
  height: auto;
}

/* Teach */
.teach-section {
  position: relative;
}

.teach-graph-visual {
  background: url('/images/math-chart.png') left center/contain no-repeat;
  width: 100%;
  height: 460px;
}

.teach-section .section-container {
  grid-template-columns: 1.3fr 1fr;
  gap: 80px;
}

/* Test */
.test-section {
  background: #3F51B5;
  color: #fff;
  position: relative;
}

.test-section h2,
.test-section .feature-subtitle,
.test-section .feature-description {
  color: #fff;
}

.test-prototype-image {
  display: block;
  width: 100%;
  height: auto;
}

/* Track */
.track-section {
  position: relative;
}

.track-prototype-image {
  display: block;
  width: 100%;
  height: auto;
}

/* Study tools two-column */
.tools-section {
  background: #E3E9FA;
}

.study-suite {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 60px;
  align-items: start;
}

.suite-left {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suite-heading {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  letter-spacing: -0.02em;
  font-size: clamp(40px, 6vw, 64px);
  color: #53575F;
}

.suite-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(16px, 2vw, 22px);
  color: #5B6279;
  line-height: 1.6;
}

.suite-subtitle .highlight {
  background: linear-gradient(90deg, #4C6FFF 0%, #06B6D4 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.suite-illustration {
  margin-top: 22px;
  margin-left: -20px;
  width: 100%;
  height: 340px;
  border-radius: 18px;
  box-shadow: 0 24px 60px rgba(76,111,255,0.25);
}

.suite-right {
  padding-top: 18px;
}

.suite-list {
  display: grid;
  gap: 18px;
}

.suite-item {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 14px;
  align-items: start;
}

.suite-badge {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  border: 2px solid #4C6FFF;
  background: #F4F7FF;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #4C6FFF;
  font-size: 18px;
  box-shadow: 0 6px 16px rgba(76,111,255,0.15);
}

.suite-item h4 {
  margin: 0;
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: 20px;
  color: #2F3348;
}

.suite-item p {
  margin-top: 6px;
  font-size: 14px;
  color: #5F6783;
  line-height: 1.5;
}

/* CTA section */
.cta-section {
  background: #1e3a8a;
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.cta-left {
  text-align: left;
}

.cta-left h2 {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(40px, 6vw, 64px);
  color: #fff;
  margin-bottom: 20px;
  line-height: 1.2;
}

.cta-subtitle {
  color: #fff;
  opacity: 0.9;
  font-size: clamp(18px, 2.5vw, 24px);
  margin-bottom: 32px;
  line-height: 1.4;
}

.cta-button-large {
  background: rgba(59, 130, 246, 0.2);
  color: #fff;
  padding: 18px 40px;
  border-radius: 50px;
  border: 2px solid #60a5fa;
  text-decoration: none;
  font-weight: 600;
  font-size: clamp(16px, 2vw, 20px);
  display: inline-block;
  transition: all 0.3s ease;
}

.cta-button-large:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.cta-right {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-bottom {
  width: 100%;
  height: 100%;
  background: url('/images/character-bottom.png') center/contain no-repeat;
  opacity: 0.9;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive tweaks */
@media (max-width: 768px) {
  .hero-section {
    padding-top: 90px;
    min-height: auto;
  }

  .hero-philosophy {
    padding: 24px 16px;
  }

  .section-container {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .suite-illustration {
    height: 260px;
  }

  /* CTA responsive */
  .cta-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .cta-left {
    text-align: center;
  }

  .cta-right {
    height: 300px;
  }

  .character-bottom {
    display: none;
  }
}

.fp-section-leaving {
  opacity: 0.8;
}

.fp-section-active {
  opacity: 1;
}
</style>
