<template>
  <div id="fullpage">
    <!-- Section 1: Hero -->
    <div class="section hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">Beyond the Answer, Towards Thinking</h1>
          <p class="hero-subtitle">An AI tutor that teaches how to think, not just answer.</p>
          <button class="cta-button" @click="goHome">Start For Free</button>
        </div>
        <div class="hero-visual">
          <img src="/images/learner.png" alt="Learner" class="hero-image" />
        </div>
      </div>
    </div>

    <!-- Section 2: Talk -->
    <div class="section talk-section">
      <div class="section-content">
        <div class="feature-visual">
          <img src="/images/thinky-talk-prototype2.png" alt="Talk Feature" class="feature-image" />
        </div>
        <div class="feature-text">
          <h2 class="feature-title">Talk</h2>
          <h3 class="feature-subtitle">Learn through dialogue</h3>
          <p class="feature-description">Smooth conversations that feel like a real teacher guiding you.</p>
        </div>
      </div>
    </div>

    <!-- Section 3: Think -->
    <div class="section think-section">
      <div class="section-content">
        <div class="feature-text">
          <h2 class="feature-title">Think</h2>
          <h3 class="feature-subtitle">Think before the answer</h3>
          <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
        </div>
        <div class="feature-visual">
          <img src="/images/thinky-think-prototype.png" alt="Think Feature" class="feature-image" />
        </div>
      </div>
    </div>

    <!-- Section 4: Teach -->
    <div class="section teach-section">
      <div class="section-content">
        <div class="feature-visual">
          <div class="teach-graph-visual"></div>
        </div>
        <div class="feature-text">
          <h2 class="feature-title">Teach</h2>
          <h3 class="feature-subtitle">Turn complexity into clarity</h3>
          <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
        </div>
      </div>
    </div>

    <!-- Section 5: Test -->
    <div class="section test-section">
      <div class="section-content">
        <div class="feature-text">
          <h2 class="feature-title">Test</h2>
          <h3 class="feature-subtitle">Spot weaknesses fast</h3>
          <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
        </div>
        <div class="feature-visual">
          <img src="/images/thinky-test-prototype.png" alt="Test Feature" class="feature-image" />
        </div>
      </div>
    </div>

    <!-- Section 6: Track -->
    <div class="section track-section">
      <div class="section-content">
        <div class="feature-visual">
          <img src="/images/thinky-track-prototype.png" alt="Track Feature" class="feature-image" />
        </div>
        <div class="feature-text">
          <h2 class="feature-title">Track</h2>
          <h3 class="feature-subtitle">See your growth</h3>
          <p class="feature-description">Knowledge graphs and insights show progress over time.</p>
        </div>
      </div>
    </div>

    <!-- Section 7: Study Tools -->
    <div class="section tools-section">
      <div class="tools-content">
        <div class="tools-left">
          <h2 class="tools-title">Study Tool Suite</h2>
          <p class="tools-subtitle">A set of AI tools to make learning <span class="highlight">faster</span> and <span class="highlight">easier</span>.</p>
        </div>
        <div class="tools-right">
          <div class="tools-grid">
            <div class="tool-item">
              <div class="tool-icon"><i class="fas fa-clipboard"></i></div>
              <div class="tool-content">
                <h4>Note-Taking AI</h4>
                <p>Instantly generate notes and outlines from course materials.</p>
              </div>
            </div>
            <div class="tool-item">
              <div class="tool-icon"><i class="fas fa-file-alt"></i></div>
              <div class="tool-content">
                <h4>Exam Simulation AI</h4>
                <p>Automatically create mock exams and simulate real test environments.</p>
              </div>
            </div>
            <div class="tool-item">
              <div class="tool-icon"><i class="fas fa-circle-xmark"></i></div>
              <div class="tool-content">
                <h4>Error Bank AI</h4>
                <p>Automatically organizes mistakes, supports targeted practice and review.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section 8: CTA -->
    <div class="section cta-section">
      <div class="cta-content">
        <div class="cta-text">
          <h2 class="cta-title">Ready to ace the study?</h2>
          <p class="cta-subtitle">Sign up to revolutionise your learning!</p>
          <button class="cta-button-large" @click="goHome">Start For Free</button>
        </div>
        <div class="cta-visual">
          <div class="character-bottom"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="fp-nav">
    <div class="nav-brand">
      <img src="/images/logo1.png" alt="Thinky Logo" class="logo-image" />
    </div>
  </nav>
</template>

<script>
import fullpage from 'fullpage.js'
import 'fullpage.js/dist/fullpage.min.css'

export default {
  name: 'FullPageLanding',
  mounted() {
    this.$nextTick(() => {
      this.initFullPage()
    })
  },
  methods: {
    initFullPage() {
      console.log('Initializing fullPage.js...')
      const fp = new fullpage('#fullpage', {
        navigation: true,
        navigationPosition: 'right',
        navigationTooltips: ['Hero', 'Talk', 'Think', 'Teach', 'Test', 'Track', 'Tools', 'Get Started'],
        showActiveTooltip: true,
        scrollingSpeed: 700,
        autoScrolling: true,
        keyboardScrolling: true,
        verticalCentered: true,
        sectionsColor: ['#ffffff', '#ffffff', '#E3E9FA', '#ffffff', '#3F51B5', '#ffffff', '#E3E9FA', '#1e3a8a'],
        sectionSelector: '.section',
        credits: { enabled: false },
        onLeave: function(origin, destination, direction) {
          console.log('Leaving section:', origin.index, 'to:', destination.index)
          const section = origin.item
          section.classList.add('fp-section-leaving')
        },
        afterLoad: function(origin, destination, direction) {
          console.log('Loaded section:', destination.index)
          const section = destination.item
          section.classList.add('fp-section-active')
          const animatedElements = section.querySelectorAll('.feature-text, .feature-visual, .hero-text, .hero-visual')
          animatedElements.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add('animate-in')
            }, index * 200)
          })
        }
      })
      console.log('fullPage.js initialized:', fp)
    },
    goHome() {
      this.$router.push('/home')
    }
  },
  beforeUnmount() {
    if (window.fullpage_api) {
      window.fullpage_api.destroy('all')
    }
  }
}
</script>

<style scoped>
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 基础样式 */
html, body {
  height: 100%;
  width: 100%;
}

#fullpage {
  height: 100vh;
  width: 100vw;
}

.fp-nav {
  position: fixed !important;
  top: 20px !important;
  left: 20px !important;
  right: auto !important;
  z-index: 1000 !important;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 48px;
  width: auto;
  object-fit: contain;
}

#fp-nav ul li a span {
  background: rgba(255, 255, 255, 0.5) !important;
  width: 8px !important;
  height: 8px !important;
}

#fp-nav ul li a.active span {
  background: #3F51B5 !important;
  width: 12px !important;
  height: 12px !important;
}

#fp-nav ul li .fp-tooltip {
  background: #3F51B5 !important;
  color: white !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
}

.section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  position: relative;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.hero-text {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.hero-text.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: clamp(32px, 4vw, 56px);
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: clamp(16px, 2vw, 24px);
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cta-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.hero-visual {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s ease 0.2s;
}

.hero-visual.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.hero-image {
  width: 100%;
  height: auto;
  max-width: 400px;
}

.section-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.feature-text, .feature-visual {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.feature-text.animate-in, .feature-visual.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.feature-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(48px, 6vw, 80px);
  color: #535353;
  margin-bottom: 20px;
  line-height: 1;
}

.feature-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: clamp(20px, 2.5vw, 28px);
  color: #000;
  margin-bottom: 16px;
}

.feature-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(14px, 1.5vw, 18px);
  color: #535353;
  line-height: 1.6;
}

.feature-image {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

.think-section .feature-title,
.think-section .feature-subtitle,
.think-section .feature-description {
  color: #2F3348;
}

.test-section .feature-title,
.test-section .feature-subtitle,
.test-section .feature-description {
  color: white;
}

.track-section .feature-subtitle {
  color: #111827;
}

.teach-graph-visual {
  background: url('/images/math-chart.png') center/contain no-repeat;
  width: 100%;
  height: 400px;
  border-radius: 12px;
}

.tools-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.tools-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: clamp(32px, 4vw, 48px);
  color: #53575F;
  margin-bottom: 16px;
}

.tools-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(14px, 1.5vw, 18px);
  color: #5B6279;
  line-height: 1.6;
}

.highlight {
  background: linear-gradient(90deg, #4C6FFF 0%, #06B6D4 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
}

.tools-grid {
  display: grid;
  gap: 16px;
}

.tool-item {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 12px;
  align-items: start;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.tool-item:hover {
  transform: translateY(-4px);
}

.tool-icon {
  width: 40px;
  height: 40px;
  background: #F4F7FF;
  border: 2px solid #4C6FFF;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4C6FFF;
  font-size: 16px;
}

.tool-content h4 {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #2F3348;
  margin-bottom: 6px;
}

.tool-content p {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #5F6783;
  line-height: 1.4;
}

.cta-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.cta-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(32px, 4vw, 48px);
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
}

.cta-subtitle {
  color: white;
  opacity: 0.9;
  font-size: clamp(16px, 2vw, 20px);
  margin-bottom: 32px;
  line-height: 1.4;
}

.cta-button-large {
  background: rgba(59, 130, 246, 0.2);
  color: white;
  border: 2px solid #60a5fa;
  padding: 18px 40px;
  border-radius: 50px;
  font-size: clamp(16px, 2vw, 20px);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cta-button-large:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.character-bottom {
  width: 100%;
  height: 300px;
  background: url('/images/character-bottom.png') center/contain no-repeat;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .section {
    padding: 0 20px;
  }

  .hero-content,
  .section-content,
  .tools-content,
  .cta-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .tools-grid {
    gap: 16px;
  }

  .tool-item {
    padding: 16px;
  }

  .character-bottom {
    height: 200px;
  }
}

.fp-section-leaving {
  opacity: 0.8;
}

.fp-section-active {
  opacity: 1;
}
</style>
