<template>
  <div id="fullpage">
    <!-- Section 1: Hero -->
    <div class="section hero-section">
      <div class="hero-content">
        <h1 class="hero-title">Beyond the Answer, Towards Thinking</h1>
        <p class="hero-subtitle">An AI tutor that teaches how to think, not just answer.</p>
        <button class="cta-button" @click="goHome">Start For Free</button>
      </div>
    </div>

    <!-- Section 2: Talk -->
    <div class="section talk-section">
      <div class="section-content">
        <h2 class="feature-title">Talk</h2>
        <h3 class="feature-subtitle">Learn through dialogue</h3>
        <p class="feature-description">Smooth conversations that feel like a real teacher guiding you.</p>
      </div>
    </div>

    <!-- Section 3: Think -->
    <div class="section think-section">
      <div class="section-content">
        <h2 class="feature-title">Think</h2>
        <h3 class="feature-subtitle">Think before the answer</h3>
        <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
      </div>
    </div>

    <!-- Section 4: Teach -->
    <div class="section teach-section">
      <div class="section-content">
        <h2 class="feature-title">Teach</h2>
        <h3 class="feature-subtitle">Turn complexity into clarity</h3>
        <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
      </div>
    </div>

    <!-- Section 5: Test -->
    <div class="section test-section">
      <div class="section-content">
        <h2 class="feature-title">Test</h2>
        <h3 class="feature-subtitle">Spot weaknesses fast</h3>
        <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
      </div>
    </div>

    <!-- Section 6: CTA -->
    <div class="section cta-section">
      <div class="cta-content">
        <h2 class="cta-title">Ready to ace the study?</h2>
        <p class="cta-subtitle">Sign up to revolutionise your learning!</p>
        <button class="cta-button-large" @click="goHome">Start For Free</button>
      </div>
    </div>
  </div>
</template>

<script>
import fullpage from 'fullpage.js'
import 'fullpage.js/dist/fullpage.min.css'

export default {
  name: 'SimpleFullPageLanding',
  mounted() {
    this.initFullPage()
  },
  methods: {
    initFullPage() {
      new fullpage('#fullpage', {
        navigation: true,
        navigationPosition: 'right',
        navigationTooltips: ['Hero', 'Talk', 'Think', 'Teach', 'Test', 'Get Started'],
        showActiveTooltip: true,
        css3: true,
        scrollingSpeed: 700,
        autoScrolling: true,
        fitToSection: true,
        keyboardScrolling: true,
        verticalCentered: true,
        sectionsColor: ['#667eea', '#ffffff', '#E3E9FA', '#ffffff', '#3F51B5', '#1e3a8a'],
        sectionSelector: '.section',
        credits: { enabled: false }
      })
    },
    goHome() {
      this.$router.push('/home')
    }
  },
  beforeUnmount() {
    if (window.fullpage_api) {
      window.fullpage_api.destroy('all')
    }
  }
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  text-align: center;
}

.hero-section {
  color: white;
}

.hero-content {
  max-width: 800px;
}

.hero-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: clamp(32px, 4vw, 56px);
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: clamp(16px, 2vw, 24px);
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cta-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.section-content {
  max-width: 800px;
}

.feature-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(48px, 6vw, 80px);
  color: #535353;
  margin-bottom: 20px;
  line-height: 1;
}

.feature-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: clamp(20px, 2.5vw, 28px);
  color: #000;
  margin-bottom: 16px;
}

.feature-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(14px, 1.5vw, 18px);
  color: #535353;
  line-height: 1.6;
}

.think-section .feature-title,
.think-section .feature-subtitle,
.think-section .feature-description {
  color: #2F3348;
}

.test-section .feature-title,
.test-section .feature-subtitle,
.test-section .feature-description {
  color: white;
}

.cta-content {
  max-width: 800px;
}

.cta-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(32px, 4vw, 48px);
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
}

.cta-subtitle {
  color: white;
  opacity: 0.9;
  font-size: clamp(16px, 2vw, 20px);
  margin-bottom: 32px;
  line-height: 1.4;
}

.cta-button-large {
  background: rgba(59, 130, 246, 0.2);
  color: white;
  border: 2px solid #60a5fa;
  padding: 18px 40px;
  border-radius: 50px;
  font-size: clamp(16px, 2vw, 20px);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cta-button-large:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .section {
    padding: 0 20px;
  }
}
</style>
