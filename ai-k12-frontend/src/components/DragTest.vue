<template>
  <div class="drag-test-container">
    <h1>拖动功能测试</h1>
    <p>尝试拖动右下角的女老师头像</p>
    
    <!-- 可拖动的女老师头像 -->
    <div 
      class="teacher-corner"
      :style="{ left: teacherPosition.x + 'px', top: teacherPosition.y + 'px' }"
      @mousedown="startDragTeacher"
      @touchstart="startDragTeacher"
    >
      <div class="teacher-image-container">
        <img src="../assets/images/teacher-avatar-4c860f.png" alt="数学老师" class="teacher-corner-image" @click="onTeacherClick" />
        <div class="teacher-speech-bubble" v-if="showTeacherTip">
          <div class="speech-content">
            {{ teacherTipText }}
          </div>
          <div class="speech-arrow"></div>
        </div>
      </div>
    </div>
    
    <!-- 调试信息 -->
    <div class="debug-info">
      <h3>调试信息</h3>
      <p>当前位置: x={{ teacherPosition.x }}, y={{ teacherPosition.y }}</p>
      <p>是否正在拖动: {{ isDraggingTeacher }}</p>
      <p>窗口大小: {{ windowSize.width }} x {{ windowSize.height }}</p>
      <button @click="resetPosition">重置位置</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DragTest',
  data() {
    return {
      // 女老师位置
      teacherPosition: { x: 0, y: 0 },
      
      // 拖动相关状态
      isDraggingTeacher: false,
      dragStartPos: { x: 0, y: 0 },
      dragOffset: { x: 0, y: 0 },
      
      // 提示相关
      showTeacherTip: false,
      teacherTipText: '我可以被拖动哦！',
      
      // 窗口大小
      windowSize: { width: 0, height: 0 }
    }
  },
  mounted() {
    this.updateWindowSize()
    this.loadTeacherPosition()
    window.addEventListener('resize', this.handleWindowResize)
    console.log('DragTest组件已挂载')
  },
  beforeUnmount() {
    // 清理事件监听器
    document.removeEventListener('mousemove', this.dragTeacher)
    document.removeEventListener('mouseup', this.stopDragTeacher)
    document.removeEventListener('touchmove', this.dragTeacher)
    document.removeEventListener('touchend', this.stopDragTeacher)
    window.removeEventListener('resize', this.handleWindowResize)
  },
  methods: {
    // 更新窗口大小
    updateWindowSize() {
      this.windowSize = {
        width: window.innerWidth,
        height: window.innerHeight
      }
    },
    
    // 加载女老师位置
    loadTeacherPosition() {
      try {
        const saved = localStorage.getItem('teacher_position_test')
        if (saved) {
          const position = JSON.parse(saved)
          if (position.x >= 0 && position.y >= 0 && 
              position.x <= window.innerWidth - 120 && 
              position.y <= window.innerHeight - 120) {
            this.teacherPosition = position
            return
          }
        }
      } catch (error) {
        console.warn('加载女老师位置失败:', error)
      }
      
      // 默认位置
      this.teacherPosition = {
        x: Math.max(20, window.innerWidth - 160),
        y: Math.max(20, window.innerHeight - 280)
      }
      console.log('设置默认位置:', this.teacherPosition)
    },
    
    // 保存女老师位置
    saveTeacherPosition() {
      try {
        localStorage.setItem('teacher_position_test', JSON.stringify(this.teacherPosition))
        console.log('保存位置:', this.teacherPosition)
      } catch (error) {
        console.warn('保存女老师位置失败:', error)
      }
    },
    
    // 重置位置
    resetPosition() {
      this.teacherPosition = {
        x: Math.max(20, window.innerWidth - 160),
        y: Math.max(20, window.innerHeight - 280)
      }
      this.saveTeacherPosition()
    },
    
    // 处理窗口大小变化
    handleWindowResize() {
      this.updateWindowSize()
      
      const maxX = Math.max(0, window.innerWidth - 120)
      const maxY = Math.max(0, window.innerHeight - 120)
      
      if (this.teacherPosition.x > maxX || this.teacherPosition.y > maxY) {
        this.teacherPosition = {
          x: Math.max(0, Math.min(this.teacherPosition.x, maxX)),
          y: Math.max(0, Math.min(this.teacherPosition.y, maxY))
        }
        this.saveTeacherPosition()
      }
    },
    
    // 开始拖动
    startDragTeacher(event) {
      event.preventDefault()
      console.log('开始拖动女老师头像')
      
      const clientX = event.type === 'touchstart' ? event.touches[0].clientX : event.clientX
      const clientY = event.type === 'touchstart' ? event.touches[0].clientY : event.clientY
      
      this.isDraggingTeacher = true
      this.dragStartPos = { x: clientX, y: clientY }
      
      const rect = event.currentTarget.getBoundingClientRect()
      this.dragOffset = {
        x: clientX - rect.left,
        y: clientY - rect.top
      }
      
      console.log('拖动开始位置:', this.dragStartPos, '偏移量:', this.dragOffset)
      
      document.addEventListener('mousemove', this.dragTeacher)
      document.addEventListener('mouseup', this.stopDragTeacher)
      document.addEventListener('touchmove', this.dragTeacher, { passive: false })
      document.addEventListener('touchend', this.stopDragTeacher)
      
      event.currentTarget.style.cursor = 'grabbing'
      event.currentTarget.style.zIndex = '2000'
    },
    
    // 拖动中
    dragTeacher(event) {
      if (!this.isDraggingTeacher) return
      
      event.preventDefault()
      
      const clientX = event.type === 'touchmove' ? 
        (event.touches && event.touches[0] ? event.touches[0].clientX : 0) : 
        (event.clientX || 0)
      const clientY = event.type === 'touchmove' ? 
        (event.touches && event.touches[0] ? event.touches[0].clientY : 0) : 
        (event.clientY || 0)
      
      let newX = clientX - this.dragOffset.x
      let newY = clientY - this.dragOffset.y
      
      const maxX = Math.max(0, window.innerWidth - 120)
      const maxY = Math.max(0, window.innerHeight - 120)
      
      newX = Math.max(0, Math.min(newX, maxX))
      newY = Math.max(0, Math.min(newY, maxY))
      
      this.teacherPosition = { x: newX, y: newY }
    },
    
    // 停止拖动
    stopDragTeacher(event) {
      if (!this.isDraggingTeacher) return
      
      console.log('停止拖动女老师头像')
      this.isDraggingTeacher = false
      
      document.removeEventListener('mousemove', this.dragTeacher)
      document.removeEventListener('mouseup', this.stopDragTeacher)
      document.removeEventListener('touchmove', this.dragTeacher)
      document.removeEventListener('touchend', this.stopDragTeacher)
      
      const teacherElement = document.querySelector('.teacher-corner')
      if (teacherElement) {
        teacherElement.style.cursor = 'grab'
        teacherElement.style.zIndex = '1500'
      }
      
      this.saveTeacherPosition()
      
      // 检查是否是点击
      const clientX = event.type === 'touchend' ? 
        (event.changedTouches && event.changedTouches[0] ? event.changedTouches[0].clientX : this.dragStartPos.x) : 
        (event.clientX || this.dragStartPos.x)
      const clientY = event.type === 'touchend' ? 
        (event.changedTouches && event.changedTouches[0] ? event.changedTouches[0].clientY : this.dragStartPos.y) : 
        (event.clientY || this.dragStartPos.y)
        
      const moveDistance = Math.sqrt(
        Math.pow(clientX - this.dragStartPos.x, 2) + 
        Math.pow(clientY - this.dragStartPos.y, 2)
      )
      
      if (moveDistance < 5) {
        setTimeout(() => {
          this.onTeacherClick(event)
        }, 10)
      }
    },
    
    // 点击女老师
    onTeacherClick(event) {
      if (this.isDraggingTeacher) return
      
      console.log('点击女老师头像')
      this.showTeacherTip = true
      this.teacherTipText = '我被点击了！位置: (' + Math.round(this.teacherPosition.x) + ', ' + Math.round(this.teacherPosition.y) + ')'
      
      setTimeout(() => {
        this.showTeacherTip = false
      }, 3000)
    }
  }
}
</script>

<style scoped>
.drag-test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.debug-info {
  position: fixed;
  top: 20px;
  left: 20px;
  background: white;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
}

.debug-info h3 {
  margin-top: 0;
}

.debug-info button {
  margin-top: 10px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-info button:hover {
  background: #0056b3;
}

/* 女老师样式 */
.teacher-corner {
  position: fixed;
  z-index: 1500;
  pointer-events: none;
  cursor: grab;
  user-select: none;
  transition: transform 0.2s ease;
}

.teacher-corner:active {
  cursor: grabbing;
}

.teacher-image-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.teacher-corner-image {
  width: 120px;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: teacherFloat 4s ease-in-out infinite;
  pointer-events: auto;
  cursor: pointer;
}

.teacher-corner-image:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

@keyframes teacherFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.teacher-speech-bubble {
  position: absolute;
  bottom: 100%;
  right: 10px;
  margin-bottom: 15px;
  background: white;
  border-radius: 18px;
  padding: 12px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  pointer-events: auto;
  animation: bubbleAppear 0.3s ease-out;
}

.speech-content {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.speech-arrow {
  position: absolute;
  bottom: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

@keyframes bubbleAppear {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
