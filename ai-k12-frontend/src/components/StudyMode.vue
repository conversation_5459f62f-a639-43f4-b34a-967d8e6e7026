<template>
  <div class="study-mode-overlay" @click="handleOverlayClick">
    <div class="study-mode-container" @click.stop>
      <!-- 分屏模式标题 -->
      <div class="study-mode-header">
        <h2 class="study-title">分屏学习模式</h2>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>

      <!-- 分屏内容区域 -->
      <div class="split-screen">
        <!-- 左侧：用户输入区域 -->
        <div class="left-panel">
          <div class="panel-header">
            <h3>📝 我的问题</h3>
            <div class="subject-grade-info" v-if="subject && grade">
              <span class="subject-tag">{{ subject }}</span>
              <span class="grade-tag">{{ grade }}</span>
            </div>
          </div>
          
          <div class="input-section">
            <!-- 输入类型选择 -->
            <div class="input-type-tabs">
              <button 
                class="tab-btn" 
                :class="{ active: inputType === 'text' }"
                @click="inputType = 'text'"
              >
                💬 文字
              </button>
              <button 
                class="tab-btn" 
                :class="{ active: inputType === 'image' }"
                @click="inputType = 'image'"
              >
                📷 图片
              </button>
              <button 
                class="tab-btn" 
                :class="{ active: inputType === 'voice' }"
                @click="inputType = 'voice'"
              >
                🎤 语音
              </button>
            </div>

            <!-- 文字输入 -->
            <div v-if="inputType === 'text'" class="text-input-area">
              <textarea
                v-model="textInput"
                placeholder="请输入您的问题或想要学习的内容..."
                rows="8"
                @input="autoResize"
                ref="textareaRef"
              ></textarea>
              <div class="input-footer">
                <span class="char-count">{{ textInput.length }}/1000</span>
                <button class="submit-btn" @click="submitQuestion" :disabled="!textInput.trim()">
                  提交问题
                </button>
              </div>
            </div>

            <!-- 图片输入 -->
            <div v-if="inputType === 'image'" class="image-input-area">
              <div class="image-upload-zone" @click="selectImage" @drop="handleDrop" @dragover.prevent>
                <div v-if="!selectedImage" class="upload-placeholder">
                  <div class="upload-icon">📷</div>
                  <p>点击上传或拖拽图片到此处</p>
                  <small>支持 JPG、PNG、GIF 格式，最大 10MB</small>
                </div>
                <div v-else class="image-preview">
                  <img :src="selectedImage" alt="上传的图片" />
                  <div class="image-actions">
                    <button class="action-btn" @click.stop="selectImage">重新选择</button>
                    <button class="action-btn remove" @click.stop="removeImage">移除</button>
                  </div>
                </div>
              </div>
              <div v-if="selectedImage" class="image-description">
                <textarea
                  v-model="imageDescription"
                  placeholder="请描述图片中的问题或添加说明..."
                  rows="3"
                ></textarea>
                <button class="submit-btn" @click="submitImage" :disabled="!selectedImage">
                  提交图片问题
                </button>
              </div>
            </div>

            <!-- 语音输入 -->
            <div v-if="inputType === 'voice'" class="voice-input-area">
              <div class="voice-recorder">
                <div class="recorder-visual" :class="{ recording: isRecording }">
                  <div class="voice-wave" v-for="i in 5" :key="i" :style="{ animationDelay: i * 0.1 + 's' }"></div>
                </div>
                <div class="recorder-controls">
                  <button 
                    class="voice-btn"
                    :class="{ recording: isRecording }"
                    @click="toggleRecording"
                  >
                    {{ isRecording ? '⏹️ 停止录音' : '🎤 开始录音' }}
                  </button>
                  <div v-if="recordedAudio" class="audio-playback">
                    <button @click="playRecording">{{ isPlaying ? '⏸️' : '▶️' }} 播放录音</button>
                    <button @click="clearRecording" class="clear-btn">🗑️ 清除</button>
                  </div>
                </div>
                <div v-if="recordedAudio" class="voice-footer">
                  <button class="submit-btn" @click="submitVoice">提交语音问题</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：AI回答区域 -->
        <div class="right-panel">
          <div class="panel-header">
            <h3>🤖 AI 助手回答</h3>
            <div class="ai-status" :class="{ thinking: isAIThinking }">
              {{ isAIThinking ? '🤔 正在思考...' : '💡 准备就绪' }}
            </div>
          </div>
          
          <div class="ai-response-area">
            <div v-if="aiResponses.length === 0" class="empty-state">
              <div class="empty-icon">🎯</div>
              <h4>等待您的问题</h4>
              <p>在左侧输入您的问题，AI助手将为您提供详细的解答和学习指导</p>
              <div class="feature-list">
                <div class="feature-item">✅ 步骤化解答</div>
                <div class="feature-item">✅ 知识点扩展</div>
                <div class="feature-item">✅ 相关练习推荐</div>
              </div>
            </div>
            
            <div v-else class="responses-list" ref="responsesContainer">
              <div 
                v-for="(response, index) in aiResponses" 
                :key="index"
                class="response-item"
              >
                <div class="response-header">
                  <span class="response-index">问题 {{ index + 1 }}</span>
                  <span class="response-time">{{ formatTime(response.timestamp) }}</span>
                </div>
                
                <div class="response-content">
                  <div class="user-question">
                    <h4>📋 您的问题</h4>
                    <div class="question-content">
                      <p v-if="response.question.text">{{ response.question.text }}</p>
                      <img v-if="response.question.image" :src="response.question.image" alt="问题图片" class="question-image" />
                      <div v-if="response.question.audio" class="question-audio">
                        <button @click="playAudio(response.question.audio)">🔊 播放语音问题</button>
                      </div>
                    </div>
                  </div>
                  
                  <div class="ai-answer">
                    <h4>🤖 AI 解答</h4>
                    <div class="answer-content" v-html="formatAIResponse(response.answer)"></div>
                    
                    <!-- 扩展学习内容 -->
                    <div v-if="response.extensions" class="extensions">
                      <h5>🎯 扩展学习</h5>
                      <div class="extension-tabs">
                        <button 
                          v-for="tab in extensionTabs" 
                          :key="tab.key"
                          class="ext-tab"
                          :class="{ active: activeExtensionTab === tab.key }"
                          @click="activeExtensionTab = tab.key"
                        >
                          {{ tab.label }}
                        </button>
                      </div>
                      <div class="extension-content">
                        <div v-if="activeExtensionTab === 'knowledge'">
                          <h6>💡 相关知识点</h6>
                          <ul>
                            <li v-for="point in response.extensions.knowledge" :key="point">{{ point }}</li>
                          </ul>
                        </div>
                        <div v-if="activeExtensionTab === 'practice'">
                          <h6>📝 练习题推荐</h6>
                          <div v-for="(practice, idx) in response.extensions.practice" :key="idx" class="practice-item">
                            <p>{{ practice.question }}</p>
                            <button class="practice-btn" @click="showAnswer(idx)">查看答案</button>
                          </div>
                        </div>
                        <div v-if="activeExtensionTab === 'tips'">
                          <h6>💭 学习技巧</h6>
                          <div v-for="tip in response.extensions.tips" :key="tip" class="tip-item">
                            {{ tip }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileSelect" 
      accept="image/*" 
      style="display: none"
    />
  </div>
</template>

<script>
import { marked } from 'marked'

export default {
  name: 'StudyMode',
  props: {
    subject: {
      type: String,
      default: ''
    },
    grade: {
      type: String,
      default: ''
    }
  },
  emits: ['close'],
  data() {
    return {
      inputType: 'text',
      textInput: '',
      selectedImage: null,
      imageDescription: '',
      recordedAudio: null,
      isRecording: false,
      isPlaying: false,
      isAIThinking: false,
      aiResponses: [],
      activeExtensionTab: 'knowledge',
      extensionTabs: [
        { key: 'knowledge', label: '知识点' },
        { key: 'practice', label: '练习题' },
        { key: 'tips', label: '学习技巧' }
      ],
      mediaRecorder: null,
      audioChunks: []
    }
  },
  methods: {
    handleOverlayClick() {
      this.$emit('close')
    },
    
    autoResize() {
      this.$nextTick(() => {
        const textarea = this.$refs.textareaRef
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = textarea.scrollHeight + 'px'
        }
      })
    },
    
    selectImage() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过 10MB')
          return
        }
        
        const reader = new FileReader()
        reader.onload = (e) => {
          this.selectedImage = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    
    handleDrop(event) {
      event.preventDefault()
      const files = event.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]
        if (file.type.startsWith('image/')) {
          this.handleFileSelect({ target: { files: [file] } })
        }
      }
    },
    
    removeImage() {
      this.selectedImage = null
      this.imageDescription = ''
    },
    
    async toggleRecording() {
      if (this.isRecording) {
        this.stopRecording()
      } else {
        await this.startRecording()
      }
    },
    
    async startRecording() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        this.mediaRecorder = new MediaRecorder(stream)
        this.audioChunks = []
        
        this.mediaRecorder.ondataavailable = (event) => {
          this.audioChunks.push(event.data)
        }
        
        this.mediaRecorder.onstop = () => {
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' })
          this.recordedAudio = URL.createObjectURL(audioBlob)
          stream.getTracks().forEach(track => track.stop())
        }
        
        this.mediaRecorder.start()
        this.isRecording = true
      } catch (error) {
        console.error('录音失败:', error)
        alert('无法访问麦克风，请检查权限设置')
      }
    },
    
    stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.mediaRecorder.stop()
        this.isRecording = false
      }
    },
    
    playRecording() {
      if (this.recordedAudio) {
        const audio = new Audio(this.recordedAudio)
        this.isPlaying = true
        audio.play()
        audio.onended = () => {
          this.isPlaying = false
        }
      }
    },
    
    clearRecording() {
      this.recordedAudio = null
      this.isPlaying = false
    },
    
    submitQuestion() {
      if (!this.textInput.trim()) return
      
      this.isAIThinking = true
      const question = {
        type: 'text',
        text: this.textInput,
        timestamp: new Date()
      }
      
      // 模拟AI处理
      setTimeout(() => {
        this.generateAIResponse(question)
        this.textInput = ''
        this.isAIThinking = false
      }, 2000)
    },
    
    submitImage() {
      if (!this.selectedImage) return
      
      this.isAIThinking = true
      const question = {
        type: 'image',
        image: this.selectedImage,
        text: this.imageDescription,
        timestamp: new Date()
      }
      
      setTimeout(() => {
        this.generateAIResponse(question)
        this.selectedImage = null
        this.imageDescription = ''
        this.isAIThinking = false
      }, 3000)
    },
    
    submitVoice() {
      if (!this.recordedAudio) return
      
      this.isAIThinking = true
      const question = {
        type: 'voice',
        audio: this.recordedAudio,
        timestamp: new Date()
      }
      
      setTimeout(() => {
        this.generateAIResponse(question)
        this.clearRecording()
        this.isAIThinking = false
      }, 2500)
    },
    
    generateAIResponse(question) {
      const mockResponse = {
        question: question,
        answer: this.getMockAIAnswer(question),
        extensions: this.getMockExtensions(),
        timestamp: new Date()
      }
      
      this.aiResponses.push(mockResponse)
      this.scrollToLatestResponse()
    },
    
    getMockAIAnswer(question) {
      const answers = {
        text: `关于"${question.text}"的问题，让我为您详细解答：\n\n**步骤一：理解题意**\n首先我们需要分析题目中的关键信息...\n\n**步骤二：应用知识点**\n根据${this.subject}学科的相关理论...\n\n**步骤三：解题过程**\n1. 列出已知条件\n2. 找出解题思路\n3. 计算得出答案\n\n**总结**\n这类问题的关键在于...`,
        image: `我看到您上传了一张图片。根据图片内容，这是一个关于${this.subject}的${this.grade}水平问题。\n\n**图片分析：**\n- 图中显示的主要内容是...\n- 关键信息包括...\n\n**解答过程：**\n基于图片中的信息，我们可以这样分析...`,
        voice: `我听到了您的语音问题。针对您提出的${this.subject}相关问题，让我为您详细解答：\n\n**语音内容理解：**\n您询问的是关于...\n\n**详细解答：**\n根据${this.grade}的学习要求...`
      }
      
      return answers[question.type] || answers.text
    },
    
    getMockExtensions() {
      return {
        knowledge: [
          `${this.subject}基本概念和定义`,
          `相关公式和定理`,
          `应用场景和实例`,
          `常见易错点分析`
        ],
        practice: [
          {
            question: `练习题1：根据刚才的讲解，请尝试解决类似问题...`,
            answer: `答案解析：...`
          },
          {
            question: `练习题2：拓展应用 - 如果条件发生变化...`,
            answer: `答案解析：...`
          }
        ],
        tips: [
          `💡 记忆技巧：使用联想记忆法...`,
          `💡 解题技巧：先分析题型，再选择方法...`,
          `💡 学习建议：多做练习，总结规律...`
        ]
      }
    },
    
    formatAIResponse(text) {
      if (!text || typeof text !== 'string') {
        return ''
      }
      return marked(text, { breaks: true, gfm: true })
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    scrollToLatestResponse() {
      this.$nextTick(() => {
        const container = this.$refs.responsesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },
    
    playAudio(audioUrl) {
      const audio = new Audio(audioUrl)
      audio.play()
    },
    
    showAnswer(index) {
      // 显示练习题答案的逻辑
      alert('答案功能待实现')
    }
  }
}
</script>

<style scoped>
.study-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.study-mode-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.study-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.study-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.split-screen {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.left-panel {
  border-right: 1px solid #e1e5e9;
  background: #fafbfc;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.subject-grade-info {
  display: flex;
  gap: 8px;
}

.subject-tag,
.grade-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.subject-tag {
  background: #e3f2fd;
  color: #1976d2;
}

.grade-tag {
  background: #f3e5f5;
  color: #7b1fa2;
}

.ai-status {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.ai-status.thinking {
  color: #ff9800;
  animation: pulse 1.5s infinite;
}

.input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px 20px;
}

.input-type-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.tab-btn {
  padding: 8px 16px;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-btn:hover {
  background: #f5f5f5;
}

.tab-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.text-input-area,
.image-input-area,
.voice-input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-input-area textarea {
  flex: 1;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 12px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
}

.text-input-area textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.char-count {
  font-size: 12px;
  color: #666;
}

.submit-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.image-upload-zone {
  flex: 1;
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-upload-zone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.upload-placeholder {
  text-align: center;
  color: #666;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.image-preview {
  position: relative;
  max-width: 100%;
  max-height: 100%;
}

.image-preview img {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.action-btn {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn.remove {
  background: #f44336;
}

.image-description {
  margin-top: 12px;
}

.image-description textarea {
  width: 100%;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 8px;
  font-family: inherit;
  resize: none;
  outline: none;
  margin-bottom: 8px;
}

.voice-recorder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.recorder-visual {
  display: flex;
  gap: 4px;
  height: 40px;
  align-items: end;
}

.voice-wave {
  width: 4px;
  background: #667eea;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.recorder-visual:not(.recording) .voice-wave {
  height: 10px;
}

.recorder-visual.recording .voice-wave {
  animation: voiceWave 0.8s infinite alternate;
}

@keyframes voiceWave {
  0% { height: 10px; }
  100% { height: 40px; }
}

.voice-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.voice-btn.recording {
  background: #f44336;
  animation: pulse 1s infinite;
}

.audio-playback {
  display: flex;
  gap: 8px;
  align-items: center;
}

.audio-playback button {
  background: #f5f5f5;
  border: 1px solid #e1e5e9;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-btn {
  background: #ffebee !important;
  color: #f44336 !important;
  border-color: #f44336 !important;
}

.ai-response-area {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px;
  color: #333;
}

.feature-list {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.responses-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  overflow-y: auto;
}

.response-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.response-index {
  font-weight: 600;
  color: #667eea;
}

.response-time {
  font-size: 12px;
  color: #999;
}

.user-question,
.ai-answer {
  margin-bottom: 16px;
}

.user-question h4,
.ai-answer h4 {
  margin: 0 0 8px;
  font-size: 14px;
  color: #333;
}

.question-content,
.answer-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.question-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 6px;
  margin-top: 8px;
}

.question-audio button {
  background: #e3f2fd;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #1976d2;
}

.extensions {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.extensions h5 {
  margin: 0 0 12px;
  color: #333;
  font-size: 14px;
}

.extension-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.ext-tab {
  padding: 6px 12px;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 16px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.ext-tab.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.extension-content {
  background: #fafbfc;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
}

.extension-content h6 {
  margin: 0 0 8px;
  color: #333;
  font-size: 13px;
}

.extension-content ul {
  margin: 0;
  padding-left: 16px;
}

.extension-content li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.practice-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #e1e5e9;
}

.practice-btn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-top: 4px;
}

.tip-item {
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 6px;
  border-left: 3px solid #ff9800;
  font-size: 12px;
}

/* 滚动条样式 */
.ai-response-area::-webkit-scrollbar,
.responses-list::-webkit-scrollbar {
  width: 6px;
}

.ai-response-area::-webkit-scrollbar-track,
.responses-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ai-response-area::-webkit-scrollbar-thumb,
.responses-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .study-mode-overlay {
    padding: 10px;
  }
  
  .split-screen {
    flex-direction: column;
  }
  
  .left-panel {
    border-right: none;
    border-bottom: 1px solid #e1e5e9;
    max-height: 300px;
  }
  
  .right-panel {
    flex: 2;
  }
  
  .input-type-tabs {
    flex-wrap: wrap;
  }
  
  .tab-btn {
    font-size: 12px;
    padding: 6px 12px;
  }
}
</style>
