<template>
  <div v-if="visible" class="modal-backdrop">
    <div class="modal">
      <div class="header">
        <h3>用手机扫码拍照上传</h3>
        <button class="close" @click="$emit('close')">×</button>
      </div>
      <div class="body">
        <div class="qr" v-if="qrUrl">
          <!-- 使用第三方二维码服务生成，若需本地库可替换为 qrcode -->
          <img :src="qrImageUrl" alt="QR" />
        </div>
        <div class="meta">
          <div class="countdown">二维码有效期：{{ remaining }} 秒</div>
          <div class="status">状态：{{ statusText }}</div>
          <div class="hint">请用手机浏览器或微信扫码</div>
        </div>
      </div>
      <div class="footer">
        <button class="btn" @click="restart" :disabled="restarting">刷新二维码</button>
      </div>
    </div>
  </div>
</template>

<script>
import { startMobileUploadSession, openUploadSSE, buildMobileUploadUrl, getSessionStatus } from '../../api/mobileUploadApi.js'

export default {
  name: 'DesktopQrModal',
  props: {
    visible: { type: Boolean, default: false },
    memoryId: { type: String, required: true }
  },
  emits: ['close', 'uploaded'],
  data() {
    return {
      sessionId: '',
      token: '',
      expiresAt: '',
      qrUrl: '',
      es: null,
      remaining: 0,
      timer: null,
      statusText: '等待扫描...',
      restarting: false,
      pollTimer: null
    }
  },
  computed: {
    qrImageUrl() {
      if (!this.qrUrl) return ''
      const encoded = encodeURIComponent(this.qrUrl)
      return `https://api.qrserver.com/v1/create-qr-code/?size=220x220&data=${encoded}`
    }
  },
  watch: {
    visible(val) {
      if (val) this.bootstrap()
      else this.cleanup()
    }
  },
  methods: {
    // 将后端返回的相对地址拼接成完整可访问地址
    // 生产站点优先使用当前页面 origin，避免出现 http://localhost:8081 导致的混合内容问题。
    resolveImageUrl(u) {
      if (!u) return ''
      if (/^https?:\/\//i.test(u)) return u
      const origin = window.location.origin.replace(/\/+$/, '')
      const path = u.startsWith('/') ? u : ('/' + u)
      return origin + path
    },
    // 选择一个安全可加载的图片 URL：优先同源（由 imageUrl 解析），
    // 仅在缺失时才退回 publicUrl，避免 http://localhost 混合内容。
    pickSafeUrl(publicUrl, imageUrl) {
      if (imageUrl) return this.resolveImageUrl(imageUrl)
      if (publicUrl) return publicUrl
      return ''
    },
    async bootstrap() {
      try {
        const res = await startMobileUploadSession(this.memoryId)
        this.sessionId = res.sessionId
        this.token = res.token
        this.expiresAt = res.expiresAt
        this.qrUrl = buildMobileUploadUrl(this.token)
        this.statusText = '等待扫描...'
        this.openSSE()
        this.startCountdown()
      } catch (e) {
        console.error(e)
        this.statusText = '创建会话失败'
      }
    },
    openSSE() {
      if (this.es) { try { this.es.close() } catch {} this.es = null }
      this.es = openUploadSSE(this.sessionId, (data) => {
        if (!data || !data.type) return
        if (data.type === 'scanned') { this.statusText = '已扫描，等待上传...'; this.startStatusPolling() }
        if (data.type === 'uploading') { this.statusText = '上传中...'; this.startStatusPolling() }
        if (data.type === 'uploaded') {
          this.statusText = '上传完成'
          // 优先同源地址，避免生产环境出现 localhost 混合内容
          const url = this.pickSafeUrl(data.publicUrl, data.imageUrl)
          const payload = { imageUrl: url }
          // 通知父组件（用于在输入框中显示预览）
          this.$emit('uploaded', payload)
          // 主动关闭 SSE 与弹窗，避免依赖父组件逻辑
          try { this.es && this.es.close() } catch {}
          this.es = null
          this.$emit('close')
          this.cleanup()
        }
        if (data.type === 'expired') {
          this.statusText = '已失效，请刷新二维码'
          this.stopCountdown()
          this.stopStatusPolling()
        }
      }, (err) => {
        console.warn('SSE error', err)
      }, () => {})
    },
    async pollOnce() {
      if (!this.sessionId) return
      try {
        const s = await getSessionStatus(this.sessionId)
        if (s && s.status === 'uploaded' && (s.publicUrl || s.imageUrl)) {
          const url = this.pickSafeUrl(s.publicUrl, s.imageUrl)
          const payload = { imageUrl: url }
          this.$emit('uploaded', payload)
          try { this.es && this.es.close() } catch {}
          this.es = null
          this.$emit('close')
          this.cleanup()
        }
      } catch (_) {}
    },
    startStatusPolling() {
      this.stopStatusPolling()
      this.pollTimer = setInterval(this.pollOnce, 2000)
    },
    stopStatusPolling() {
      if (this.pollTimer) { clearInterval(this.pollTimer); this.pollTimer = null }
    },
    startCountdown() {
      this.stopCountdown()
      const end = new Date(this.expiresAt).getTime()
      const tick = () => {
        const now = Date.now()
        const diff = Math.max(0, Math.floor((end - now) / 1000))
        this.remaining = diff
        if (diff <= 0) this.stopCountdown()
      }
      tick()
      this.timer = setInterval(tick, 1000)
    },
    stopCountdown() {
      if (this.timer) { clearInterval(this.timer); this.timer = null }
    },
    restart() {
      this.restarting = true
      this.cleanup()
      this.bootstrap().finally(() => { this.restarting = false })
    },
    cleanup() {
      if (this.es) { try { this.es.close() } catch {}; this.es = null }
      this.stopCountdown()
      this.stopStatusPolling()
      this.sessionId = ''
      this.token = ''
      this.qrUrl = ''
      this.remaining = 0
    }
  },
  beforeUnmount() {
    this.cleanup()
  }
}
</script>

<style scoped>
.modal-backdrop {
  position: fixed; inset: 0; background: rgba(0,0,0,0.45);
  display:flex; align-items:center; justify-content:center; z-index: 9999;
}
.modal { width: 360px; background:#fff; border-radius:12px; overflow:hidden; }
.header { display:flex; justify-content:space-between; align-items:center; padding:12px 16px; border-bottom:1px solid #eee; }
.close { border:none; background:transparent; font-size:20px; cursor:pointer; }
.body { padding:16px; text-align:center; }
.qr img { width: 220px; height: 220px; border:1px solid #eee; border-radius:8px; }
.meta { margin-top: 12px; color:#444; font-size:14px; }
.hint { color:#888; font-size:12px; margin-top: 6px; }
.footer { padding: 10px 16px; border-top:1px solid #eee; text-align:right; }
.btn { padding:8px 12px; border-radius:8px; border:1px solid #ddd; background:#fff; cursor:pointer; }
</style>
