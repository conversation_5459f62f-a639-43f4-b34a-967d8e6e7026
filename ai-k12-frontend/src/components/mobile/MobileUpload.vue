<template>
  <div class="mobile-upload">
    <div class="container">
      <h2>拍照/相册上传</h2>
      <p v-if="!ready">正在初始化...</p>
      <p v-else class="tip">将题目完整拍入取景框，确保清晰可读。</p>

      <div class="preview" v-if="previewUrl">
        <img :src="previewUrl" alt="预览" />
      </div>

      <div class="actions">
        <!-- 相机：带 capture 强制调用摄像头 -->
        <input
          ref="fileCamera"
          type="file"
          accept="image/*"
          capture="environment"
          @change="onSelect"
          style="display:none"
        />
        <!-- 相册：不带 capture，允许选择本地相册/文件 -->
        <input
          ref="fileGallery"
          type="file"
          accept="image/*"
          @change="onSelect"
          style="display:none"
        />
        <button class="btn" @click="pickCamera">拍照上传</button>
        <button class="btn" @click="pickGallery">从相册选择</button>
      </div>

      <div class="status" v-if="status">
        {{ status }}
      </div>

      <div class="footer-note">
        <p>上传成功后，可返回电脑继续操作。</p>
      </div>
    </div>
  </div>
</template>

<script>
import { appConfig } from '../../config/env.js'

export default {
  name: 'MobileUpload',
  data() {
    return {
      token: '',
      ready: false,
      status: '',
      previewUrl: ''
    }
  },
  mounted() {
    const url = new URL(window.location.href)
    const t = url.searchParams.get('token')
    if (!t) {
      this.status = '缺少 token，无法上传'
      return
    }
    this.token = t
    this.ready = true
    // 通知后端已扫描
    fetch(`${appConfig.api.baseUrl}/mobile-upload/mark-scanned?token=${encodeURIComponent(this.token)}`, { method: 'POST' })
      .catch(() => {})
  },
  methods: {
    pickCamera() {
      this.$refs.fileCamera && this.$refs.fileCamera.click()
    },
    pickGallery() {
      this.$refs.fileGallery && this.$refs.fileGallery.click()
    },
    async onSelect(e) {
      const file = e.target.files && e.target.files[0]
      if (!file) return
      try {
        this.status = '处理中...'
        // 提升清晰度：将最长边提升到 2048，JPEG 质量 0.92；
        // 小图或体积较小则直接保留原图，避免二次压缩带来模糊。
        const processed = await this.compressImage(file, 2048, 0.92)
        this.previewUrl = URL.createObjectURL(processed)
        this.status = '上传中...'
        const fd = new FormData()
        fd.append('file', processed, file.name || 'photo.jpg')
        const res = await fetch(`${appConfig.api.baseUrl}/mobile-upload/upload?token=${encodeURIComponent(this.token)}`, {
          method: 'POST',
          body: fd
        })
        if (!res.ok) throw new Error('上传失败')
        await res.json() // { imageUrl }
        this.status = '上传成功！3秒后可关闭页面'
        setTimeout(() => window.close(), 3000)
      } catch (err) {
        console.error(err)
        this.status = '上传失败：' + err.message
      }
    },
    compressImage(file, maxSize, quality) {
      // 若图片本身较小或清晰度已足够，直接返回原文件，避免模糊。
      const SIZE_KEEP_ORIGINAL = 6 * 1024 * 1024 // 6MB
      return new Promise((resolve, reject) => {
        const img = new Image()
        const url = URL.createObjectURL(file)

        const cleanup = () => URL.revokeObjectURL(url)

        img.onload = async () => {
          try {
            let w = img.naturalWidth
            let h = img.naturalHeight
            // 不需要缩放且体积不大，直接用原图
            const ratio = w > h ? maxSize / w : maxSize / h
            if (ratio >= 1 && file.size <= SIZE_KEEP_ORIGINAL) {
              cleanup()
              resolve(file)
              return
            }

            if (ratio < 1) { w = Math.round(w * ratio); h = Math.round(h * ratio) }

            const canvas = document.createElement('canvas')
            canvas.width = w
            canvas.height = h
            const ctx = canvas.getContext('2d')
            ctx.drawImage(img, 0, 0, w, h)

            // 迭代压缩，尽量保持清晰度，同时控制在 9.5MB 以下，最多三次
            const TARGET_MAX = 9.5 * 1024 * 1024
            let q = quality
            let blob = await new Promise(res => canvas.toBlob(res, 'image/jpeg', q))
            let tries = 0
            while (blob && blob.size > TARGET_MAX && tries < 3) {
              q = Math.max(0.75, q - 0.07)
              blob = await new Promise(res => canvas.toBlob(res, 'image/jpeg', q))
              tries++
            }
            if (!blob) throw new Error('压缩失败')

            const result = new File([blob], file.name || 'photo.jpg', { type: 'image/jpeg' })
            cleanup()
            resolve(result)
          } catch (err) {
            cleanup()
            reject(err)
          }
        }
        img.onerror = () => { cleanup(); reject(new Error('读取图片失败')) }
        img.src = url
      })
    }
  }
}
</script>

<style scoped>
.mobile-upload {
  min-height: 100vh;
  background: #f7f9fb;
}
.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 24px 16px;
  text-align: center;
}
h2 { margin: 6px 0 8px; }
.tip { color:#666; font-size:14px; }
.actions { margin: 16px 0; display:flex; gap:12px; justify-content:center; }
.btn { padding:10px 14px; border-radius:8px; border:1px solid #ddd; background:#fff; }
.status { margin-top: 12px; color:#333; }
.preview img { max-width: 100%; border-radius: 8px; border:1px solid #eee; }
.footer-note { margin-top: 16px; color:#888; font-size: 13px; }
</style>
