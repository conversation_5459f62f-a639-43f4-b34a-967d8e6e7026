<template>
  <div class="chat-window">
    <!-- 右上角语言选择器 -->
    <div class="language-selector">
      <label class="language-label">Language:</label>
      <select v-model="language" class="global-lang-select" title="选择语言">
        <option value="en">🇺🇸 English</option>
        <option value="zh">🇨🇳 中文</option>
      </select>
    </div>
    
    <!-- 聊天容器 -->
    <div class="chat-container">
      <!-- 消息列表 -->
      <div v-if="messages.length > 0" class="messages-list" :class="{ 'expanded': !showInput }" ref="messagesList">
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
        >
          <div class="message-avatar">
            <div v-if="!message.isUser" class="ai-avatar">🤖</div>
            <div v-else class="user-avatar">👤</div>
          </div>
          
          <div class="message-content">
            <div class="message-bubble">
              <!-- 图片消息 -->
              <img v-if="message.image" :src="message.image" alt="Message image" class="message-image" />
              

              
              <!-- 文本消息 -->
              <div v-if="message.text" class="message-text" v-html="formatMessage(message.text)"></div>
              
              <!-- 流式响应指示器 -->
              <div v-if="message.isStreaming" class="streaming-indicator">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
            
            <div class="message-meta">
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              <button 
                v-if="!message.isUser && message.text"
                class="tts-inline-btn"
                @click="playTTSForMessage(message)"
                title="播放语音播报"
                aria-label="播放语音播报"
              >
                <svg width="14" height="14" viewBox="0 0 16 16" aria-hidden="true">
                  <path d="M3 6H5L8 3V13L5 10H3C2.45 10 2 9.55 2 9V7C2 6.45 2.45 6 3 6ZM11.5 3.5C11.78 3.22 12.25 3.22 12.53 3.5C14.1 5.07 14.1 7.68 12.53 9.25C12.25 9.53 11.78 9.53 11.5 9.25C11.22 8.97 11.22 8.5 11.5 8.22C12.64 7.08 12.64 5.67 11.5 4.53C11.22 4.25 11.22 3.78 11.5 3.5ZM9.88 5.12C10.17 4.83 10.64 4.83 10.92 5.12C11.7 5.9 11.7 7.1 10.92 7.88C10.64 8.17 10.17 8.17 9.88 7.88C9.6 7.6 9.6 7.13 9.88 6.84C10.06 6.66 10.16 6.43 10.16 6.18C10.16 5.93 10.06 5.7 9.88 5.52C9.6 5.24 9.6 4.77 9.88 4.48V5.12Z" fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div v-if="showInput" class="chat-input-container">
        <!-- 选择错误提示 -->
        <div v-if="selectionError" class="selection-error">
          <span>{{ selectionError }}</span>
        </div>

        <div class="input-wrapper">
          <!-- 图片预览 -->
          <div v-if="selectedImage" class="image-preview">
            <img :src="selectedImage" alt="Selected image" />
            <button class="remove-image" @click="removeImage">×</button>
          </div>

          <!-- 文本输入框 -->
          <textarea
            v-model="inputMessage"
            @keydown="handleKeyDown"
            @input="adjustHeight"
            ref="messageInput"
            :placeholder="placeholderText"
            rows="1"
            class="main-input"
          ></textarea>

          <!-- 功能按钮组 -->
          <div class="input-actions">
            <!-- 图片上传按钮 -->
            <button class="action-btn" @click="selectImage" title="上传图片">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M14 2H2C1.45 2 1 2.45 1 3v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zM2 3h12v6l-3-3c-.28-.28-.72-.28-1 0L6 9l-2-2c-.28-.28-.72-.28-1 0L2 9V3z" fill="currentColor"/>
              </svg>
            </button>
            
            <!-- 相机按钮 -->
            <button class="action-btn" @click="openMobileUpload" title="拍照上传">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M7 4h2l1-2h2l1 2h2c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1H5c-.55 0-1-.45-1-1V5c0-.55.45-1 1-1zm5 6c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2z" fill="currentColor"/>
              </svg>
            </button>
            

            <!-- 麦克风按钮 -->
            <button 
              class="action-btn voice-btn" 
              @click="toggleVoiceInput" 
              :class="{ 'active': isRecording }"
              title="语音输入"
            >
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M8 1c1.66 0 3 1.34 3 3v4c0 1.66 1.34 3-3 3s-3-1.34-3-3V4c0-1.66 1.34-3 3-3zm5 4v4c0 2.76-2.24 5-5 5s-5-2.24-5-5V5c0-.55.45-1 1-1s1 .45 1 1v4c0 1.66 1.34 3 3 3s3-1.34 3-3V5c0-.55.45-1 1-1s1 .45 1 1z" fill="currentColor"/>
              </svg>
            </button>
          </div>

          <!-- 发送按钮 -->
          <button 
            class="send-btn" 
            @click="sendMessage" 
            :disabled="!canSend || isLoading"
            :class="{ 'can-send': canSend && !isLoading, 'loading': isLoading }"
          >
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M2 8L14 2L10 8L14 14L2 8Z" fill="currentColor"/>
            </svg>
          </button>

          <!-- 清空对话按钮（输入框右侧） -->
          <button 
            v-if="messages.length > 0"
            class="clear-chat-inline-btn" 
            title="清空对话"
            @click="clearAllMessages"
          >
            清空对话
          </button>
        </div>
      </div>

      <!-- 输入框切换按钮 -->
      <div v-if="!showInput" class="input-toggle-container">
        <button class="input-toggle-btn" @click="showInput = true" title="显示输入框">
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path d="M10 2L10 18M10 18L10 2M2 10L18 10" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileSelect" 
      accept="image/*" 
      style="display: none"
    />

    <!-- 手机上传二维码弹窗 -->
    <DesktopQrModal 
      :visible="qrModalVisible" 
      :memory-id="memoryId"
      @close="qrModalVisible = false"
      @uploaded="handleMobileUploaded"
    />

    <!-- 隐藏的音频播放器（用于TTS自动播放） -->
    <audio ref="ttsAudio" preload="auto"></audio>

    <!-- 语音输入面板：点击麦克风后显示当前状态与停止按钮 -->
    <div 
      v-if="isRecording || isRecognizing || wsState === 'connecting' || (wsState === 'connected' && voiceStatus !== '待机') || (voiceStatus && voiceStatus !== '待机')"
      class="voice-overlay"
    >
      <div class="voice-panel">
        <div class="voice-header">
          <span class="voice-title">🎤 语音输入</span>
          <button class="voice-close" @click="stopVoiceInput" title="关闭语音输入">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        
        <div class="voice-main">
          <!-- 音频波形动画区域 -->
          <div class="voice-visual">
            <div class="wave-container" :class="{ active: isRecording }">
              <div class="wave-bar" v-for="i in 5" :key="i" :style="{
                animationDelay: (i * 0.1) + 's',
                height: isRecording ? (20 + Math.random() * 40 * (meter * 5 + 0.2)) + 'px' : '4px'
              }"></div>
            </div>
            
            <!-- 状态图标 -->
            <div class="status-icon">
              <div v-if="isRecording" class="recording-pulse">
                <div class="pulse-ring"></div>
                <div class="pulse-core"></div>
              </div>
              <div v-else-if="isRecognizing" class="processing-spinner">
                <svg class="spinner-svg" viewBox="0 0 50 50">
                  <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#007bff" stroke-width="2"></circle>
                </svg>
              </div>
              <div v-else class="standby-icon">⏸️</div>
            </div>
          </div>
          
          <!-- 状态文字 -->
          <div class="voice-status-section">
            <div class="voice-status-text">
              <template v-if="isRecording">正在聆听您的语音...</template>
              <template v-else-if="isRecognizing">正在识别语音内容...</template>
              <template v-else>{{ voiceStatus }}</template>
            </div>
            
            <div v-if="isRecording" class="voice-hint">
              清晰说话，完成后点击停止按钮
            </div>
          </div>
          
          <!-- 音量可视化 -->
          <div class="volume-section" v-if="isRecording">
            <div class="volume-label">音量</div>
            <div class="volume-meter">
              <div class="volume-bar" :style="{ 
                width: Math.max(0, Math.min(100, meter * 200)) + '%',
                background: meter > 0.3 ? '#4caf50' : meter > 0.1 ? '#ff9800' : '#f44336'
              }"></div>
            </div>
          </div>
        </div>
        
        <!-- 控制区域 -->
        <div class="voice-footer">
          <div class="voice-controls">
            
            <button 
              class="stop-voice-btn" 
              @click="stopVoiceInput"
              :disabled="isRecognizing || (!isRecording && wsState !== 'connected')"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor"/>
              </svg>
              停止
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { marked } from 'marked'
import DesktopQrModal from './mobile/DesktopQrModal.vue'
// import { speechToText } from '../api/speechApi.js' // 改为本地流式识别
import { speakText, stopSpeaking } from '../api/speechApi.js'

export default {
  name: 'ChatWindow',
  components: { DesktopQrModal },
  props: {
    subject: {
      type: String,
      default: ''
    },
    grade: {
      type: String,
      default: ''
    }
  },
  emits: ['send-message', 'messages-changed'],
  data() {
    return {
      inputMessage: '',
      selectedImage: null,  // 图片预览的 base64 URL
      selectedImageFile: null,  // 实际的图片文件对象
      messages: [],
      memoryId: Date.now().toString(), // 生成唯一的会话ID
      currentEventSource: null, // 当前的SSE连接
      isLoading: false, // 加载状态
      // 语音识别相关
      isRecording: false,
      isRecognizing: false,
      meter: 0,
      ws: null,
      wsState: 'disconnected',
      audioContext: null,
      workletNode: null,
      sourceNode: null,
      sampleRate: 0,
      language: 'en',
      floatQueue: [], // Float32 分片队列
      accFloat: null, // 累计缓冲
      frameSize: 0, // 20ms 帧大小（sampleRate/50）
      voiceStatus: '待机',
      qrModalVisible: false,
      // 输入框状态控制
      showInput: true,
      // 选择错误提示
      selectionError: '',
      // 语音播报（TTS）
      ttsEnabled: false,
      ttsObjectUrl: null,
      ttsPlaying: false,
      ttsAbortController: null
    }
  },
  computed: {
    canSend() {
      return (this.inputMessage.trim() || this.selectedImage) && !this.isLoading
    },
    
    placeholderText() {
      if (this.subject && this.grade) {
        return this.language === 'en'
          ? `Ask the ${this.grade} ${this.subject} AI assistant...`
          : `向${this.grade}${this.subject}AI助手提问...`
      }
      return this.language === 'en'
        ? "Type, snap, or say it — let's solve it together."
        : '输入、拍照或说出你的问题，让我们一起解决。'
    },
    

  },
  methods: {
    // 归一化任意图片URL为当前页面同源，避免 https 站点引用 http://localhost 导致混合内容
    sameOriginUrl(u) {
      try {
        if (!u) return ''
        // 使用当前页面 origin 作为基准，既能处理相对路径，也能取出绝对地址的 pathname
        const parsed = new URL(u, window.location.origin)
        const origin = window.location.origin.replace(/\/+$/, '')
        return origin + parsed.pathname + parsed.search + parsed.hash
      } catch (_) {
        // 回退：若是相对路径
        if (typeof u === 'string' && u.startsWith('/')) return window.location.origin.replace(/\/+$/, '') + u
        return u || ''
      }
    },
    // 手动触发：对单条AI消息进行TTS播报（不依赖全局开关）
    async playTTSForMessage(message) {
      try {
        if (!message || !message.text) return
        console.log('[ChatWindow][TTS-inline] 手动触发TTS, msgId=', message.id)
        const content = this.cleanTextForSpeech(message.text)
        // 优先服务端TTS
        await this.playViaWhisperTTS(content, this.language)
      } catch (e) {
        console.warn('[ChatWindow][TTS-inline] 服务端TTS失败，回退浏览器TTS:', e)
        try {
          await this.playViaBrowserTTS(message.text, this.language)
        } catch (err) {
          console.error('[ChatWindow][TTS-inline] 浏览器TTS也失败:', err)
        }
      }
    },
    // --- TTS 开关与播放 ---
    toggleTts() {
      this.ttsEnabled = !this.ttsEnabled
      if (!this.ttsEnabled) {
        this.stopTTS()
      }
    },
    async maybeSpeak(text) {
      try {
        if (!this.ttsEnabled) return
        const content = this.cleanTextForSpeech(text || '')
        if (!content.trim()) return
        console.log('[ChatWindow][TTS] 开始TTS合成，长度=', content.length)
        // 优先使用本地 Faster-Whisper TTS 服务
        await this.playViaWhisperTTS(content, this.language)
      } catch (e) {
        console.warn('[ChatWindow][TTS] Whisper TTS 播放失败，回退到浏览器TTS:', e)
        try {
          await this.playViaBrowserTTS(text || '', this.language)
        } catch (err) {
          console.error('[ChatWindow][TTS] 浏览器TTS也失败:', err)
        }
      }
    },
    stopTTS() {
      try {
        // 取消进行中的合成
        if (this.ttsAbortController) {
          this.ttsAbortController.abort()
          this.ttsAbortController = null
        }
      } catch (_) {}

      try {
        const audio = this.$refs.ttsAudio
        if (audio) {
          audio.pause()
          audio.currentTime = 0
        }
      } catch (_) {}

      // 停止浏览器TTS
      try { stopSpeaking() } catch (_) {}

      try {
        if (this.ttsObjectUrl) {
          URL.revokeObjectURL(this.ttsObjectUrl)
          this.ttsObjectUrl = null
        }
      } catch (_) {}

      this.ttsPlaying = false
    },
    async playViaWhisperTTS(text, lang = 'zh') {
      this.stopTTS()
      const endpoint = lang === 'en' ? '/whisper/tts/en/synthesize' : '/whisper/tts/zh/synthesize'
      const controller = new AbortController()
      this.ttsAbortController = controller
      console.log('[ChatWindow][TTS] 请求服务端TTS:', endpoint)

      const resp = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, speed: 1.0 }),
        signal: controller.signal
      })
      if (!resp.ok) {
        throw new Error(`TTS HTTP ${resp.status}`)
      }
      const blob = await resp.blob()
      console.log('[ChatWindow][TTS] 服务端返回音频，大小(byte)=', blob.size)
      const url = URL.createObjectURL(blob)
      this.ttsObjectUrl = url
      const audio = this.$refs.ttsAudio
      if (!audio) throw new Error('音频播放器未就绪')
      audio.src = url
      audio.onended = () => {
        this.ttsPlaying = false
        try { if (this.ttsObjectUrl) URL.revokeObjectURL(this.ttsObjectUrl) } catch (_) {}
        this.ttsObjectUrl = null
      }
      this.ttsPlaying = true
      try {
        await audio.play()
      } catch (e) {
        // 自动播放受限时抛出，交给上层 fallback
        throw e
      }
    },
    async playViaBrowserTTS(text, lang = 'zh') {
      const language = lang === 'en' ? 'en-US' : 'zh-CN'
      await speakText(this.cleanTextForSpeech(text || ''), { language })
    },
    cleanTextForSpeech(md = '') {
      try {
        let s = String(md)
        // 移除代码块和内联代码
        s = s.replace(/```[\s\S]*?```/g, ' ')
        s = s.replace(/`([^`]+)`/g, '$1')
        // 替换图片与链接
        s = s.replace(/!\[([^\]]*)\]\([^\)]+\)/g, '$1')
        s = s.replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '$1')
        // 去除多余 Markdown 标记
        s = s.replace(/[>*_#\-]{1,}/g, ' ')
        // 合并空白并截断过长文本
        s = s.replace(/\s+/g, ' ').trim()
        if (s.length > 4000) s = s.slice(0, 4000)
        return s
      } catch (_) {
        return md
      }
    },
    showMoreOptions() {
      // 显示更多选项菜单（图片上传、文件上传等）
      this.selectImage()
    },
    openMobileUpload() {
      this.qrModalVisible = true
    },
    
    showHistory() {
      // 显示聊天历史记录
      console.log('显示聊天历史')
      // 这里可以添加显示历史记录的逻辑
    },

    // 语音输入相关方法
    toggleVoiceInput() {
      if (this.isRecording) {
        this.stopVoiceInput()
      } else {
        this.startVoiceInput()
      }
    },

    // 统一封装麦克风请求，兼容旧浏览器并在非安全上下文给出明确提示
    async requestMicStream(constraints) {
      const isLocalhost = ['localhost', '127.0.0.1', '::1'].includes(location.hostname)
      const isSecure = location.protocol === 'https:' || isLocalhost || (window.isSecureContext === true)
      // 主流 API
      const mediaDevices = navigator && navigator.mediaDevices
      const hasStdAPI = !!(mediaDevices && mediaDevices.getUserMedia)
      // 旧版前缀 API
      const legacyGUM = (navigator && (navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia)) || null

      if (!hasStdAPI && !legacyGUM) {
        // 典型场景：非 HTTPS 域名导致 mediaDevices 不可用
        if (!isSecure) {
          throw new Error('浏览器禁用了麦克风采集：请使用 HTTPS 域名或通过反向代理启用 TLS')
        }
        throw new Error('浏览器不支持 getUserMedia（请升级浏览器或使用 Chrome/Edge/Safari 最新版）')
      }

      if (hasStdAPI) {
        return await mediaDevices.getUserMedia(constraints)
      }
      // 走旧版回退
      return await new Promise((resolve, reject) => {
        legacyGUM.call(navigator, constraints, resolve, reject)
      })
    },

    async startVoiceInput() {
      try {
        this.voiceStatus = '请求音频权限...'
        this.isRecording = true
        // 音频（带能力检测与 HTTPS 提示）
        const stream = await this.requestMicStream({ 
          audio: { 
            channelCount: 1, 
            echoCancellation: true, 
            noiseSuppression: true, 
            autoGainControl: true 
          } 
        })
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ latencyHint: 'interactive' })
        if (!this.audioContext.audioWorklet) {
          throw new Error('浏览器不支持 AudioWorklet，请更换现代浏览器')
        }
        await this.audioContext.audioWorklet.addModule('/worklets/pcm-processor.js')
        this.sampleRate = this.audioContext.sampleRate
        this.frameSize = Math.round(this.sampleRate / 50) // 20ms 帧
        this.sourceNode = this.audioContext.createMediaStreamSource(stream)
        this.workletNode = new AudioWorkletNode(this.audioContext, 'pcm-processor')
        this.workletNode.port.onmessage = (e) => {
          if (!e.data || e.data.type !== 'audio') return
          const float32 = e.data.samples
          // 计算 RMS 用于音量指示
          let sum = 0
          for (let i = 0; i < float32.length; i++) sum += float32[i] * float32[i]
          this.meter = Math.sqrt(sum / float32.length)
          // 入队
          this.enqueueFloat(float32)
          this.flushFrames()
        }
        this.sourceNode.connect(this.workletNode)
        this.workletNode.connect(this.audioContext.destination)

        // WS
        await this.openVoiceWS()
        this.voiceStatus = '录音中（流式发送）...'
      } catch (e) {
        console.error(e)
        this.voiceStatus = '启动失败: ' + e.message
        this.isRecording = false
      }
    },

    async openVoiceWS() {
      // 使用可配置的 Whisper WS 地址（默认指向 englishread.xyz 在开发环境）
      const { getWhisperWsUrl } = await import('../config/env.js')
      const url = getWhisperWsUrl()
      this.ws = new WebSocket(url)
      this.ws.binaryType = 'arraybuffer'
      this.wsState = 'connecting'
      this.ws.onopen = () => {
        this.wsState = 'connected'
        this.ws.send(JSON.stringify({ event: 'start', sampleRate: this.sampleRate, language: this.language }))
      }
      this.ws.onmessage = (evt) => {
        try {
          const msg = JSON.parse(evt.data)
          if (msg.event === 'ack') {
            this.voiceStatus = '开始流式发送...'
            this.isRecognizing = false
          } else if (msg.event === 'final') {
            const recognizedText = msg.text || ''
            this.voiceStatus = '已收到最终结果'
            this.isRecognizing = false
            // 将识别的文字添加到输入框
            if (recognizedText.trim()) {
              this.inputMessage = recognizedText.trim()
              this.adjustHeight()
            }
            try { this.ws.close() } catch {}
          } else if (msg.event === 'error') {
            this.voiceStatus = '服务错误: ' + msg.message
            this.isRecognizing = false
          }
        } catch (_) {}
      }
      this.ws.onclose = () => { this.wsState = 'disconnected' }
      this.ws.onclose = () => {
        this.wsState = 'disconnected'
        if (!this.isRecording) {
          // 连接关闭后且未录音，重置状态以隐藏面板
          this.voiceStatus = '待机'
        }
        this.isRecognizing = false
      }
      this.ws.onerror = () => { this.wsState = 'error' }
    },

    enqueueFloat(chunk) {
      if (!this.accFloat || this.accFloat.length === 0) {
        this.accFloat = chunk
      } else {
        const merged = new Float32Array(this.accFloat.length + chunk.length)
        merged.set(this.accFloat, 0)
        merged.set(chunk, this.accFloat.length)
        this.accFloat = merged
      }
    },

    flushFrames() {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return
      while (this.accFloat && this.accFloat.length >= this.frameSize) {
        const frame = this.accFloat.subarray(0, this.frameSize)
        const rest = this.accFloat.subarray(this.frameSize)
        // float32 -> int16 小端
        const ab = new ArrayBuffer(frame.length * 2)
        const view = new DataView(ab)
        for (let i = 0; i < frame.length; i++) {
          let s = Math.max(-1, Math.min(1, frame[i]))
          view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true)
        }
        this.ws.send(ab)
        // 余量保留
        const remain = new Float32Array(rest.length)
        remain.set(rest, 0)
        this.accFloat = remain
      }
    },

    stopVoiceInput() {
      // 停止音频采集，但保留 WS 连接等待服务端返回 final
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({ event: 'end' }))
        }
      } catch {}
      // 关闭本地音频链路
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      this.workletNode = null
      this.sourceNode = null
      this.audioContext = null
      this.isRecording = false
      this.voiceStatus = '已停止，等待识别结果...'
      this.isRecognizing = true
    },

    cleanupVoice() {
      this.isRecording = false
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      try { if (this.ws && this.ws.readyState === WebSocket.OPEN) this.ws.close() } catch {}
      this.ws = null
      this.accFloat = null
      // 重置界面状态，隐藏语音面板
      this.wsState = 'disconnected'
      this.voiceStatus = '待机'
      this.isRecognizing = false
    },
    
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },
    
    adjustHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
        }
      })
    },
    
    sendMessage() {
      // 检查年级和科目是否已选择
      if (!this.grade || !this.grade.trim()) {
        this.showSelectionError('请先选择年级和科目后再提问！')
        return
      }
      if (!this.subject || !this.subject.trim()) {
        this.showSelectionError('请先选择年级和科目后再提问！')
        return
      }

      if (!this.canSend || this.isLoading) return
      const raw = this.inputMessage.trim()
      const messageText = raw || (this.selectedImage ? '请解析我上传的图片' : '')
      if (!messageText && !this.selectedImage) return
      
      // 准备要传递的数据
      const questionData = {
        text: messageText,
        image: this.selectedImage,
        imageFile: this.selectedImageFile,
        subject: this.subject,
        grade: this.grade,
        memoryId: this.memoryId,
        language: this.language // 添加语言参数用于控制大模型输出语言
      }
      
      // 将数据存储到 sessionStorage，以便在课堂模式页面使用
      sessionStorage.setItem('pendingQuestion', JSON.stringify(questionData))
      
      // 跳转到课堂模式
      this.$router.push('/dual-screen')
    },
    handleMobileUploaded(payload) {
      try {
        if (payload && payload.imageUrl) {
          // 统一转换为同源 URL，避免出现 http://localhost 等
          this.selectedImage = this.sameOriginUrl(payload.imageUrl)
          this.selectedImageFile = null
          this.qrModalVisible = false
          // 确保输入框显示，用户能看到预览并继续输入
          this.showInput = true
          this.$nextTick(() => this.scrollToBottom())
        }
      } catch (_) {}
    },
    
    selectImage() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        // 保存文件对象用于API调用
        this.selectedImageFile = file
        
        // 生成预览URL
        const reader = new FileReader()
        reader.onload = (e) => {
          this.selectedImage = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    
    removeImage() {
      this.selectedImage = null
      this.selectedImageFile = null
      this.$refs.fileInput.value = ''
    },
    

    
    formatMessage(text) {
      if (!text || typeof text !== 'string') {
        return ''
      }
      return marked(text, { breaks: true, gfm: true })
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    resetTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
        }
      })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const messagesList = this.$refs.messagesList
        if (messagesList) {
          messagesList.scrollTop = messagesList.scrollHeight
        }
      })
    },
    
    // 手动显示输入框
    showInputField() {
      this.showInput = true
    },
    
    // 清空所有消息
    clearAllMessages() {
      this.messages = []
      this.showInput = true
      // 通知父组件消息状态变化
      this.$emit('messages-changed', false)
    },

    // 显示选择错误提示
    showSelectionError(message) {
      this.selectionError = message
      // 3秒后自动清除提示
      setTimeout(() => {
        this.selectionError = ''
      }, 3000)
    }
  },
  
  mounted() {
    // 确保组件挂载时输入框是显示的
    this.showInput = true
    // 默认使用英文
    try {
      this.language = 'en'
      localStorage.setItem('asrLanguage', 'en')
    } catch (_) {}
    // 恢复TTS开关
    try {
      const tts = localStorage.getItem('k12_auto_tts')
      if (tts === '1') this.ttsEnabled = true
    } catch (_) {}
  },
  
  watch: {
    messages: {
      handler(newMessages) {
        // 通知父组件消息数量变化
        this.$emit('messages-changed', newMessages.length > 0)
      },
      immediate: true
    },
    language(newLang) {
      try { 
        localStorage.setItem('asrLanguage', newLang)
        console.log('[ChatWindow] 语言切换为:', newLang)
      } catch (_) {}
    },
    ttsEnabled(val) {
      try { localStorage.setItem('k12_auto_tts', val ? '1' : '0') } catch (_) {}
    }
  },
  
  beforeUnmount() {
    // 组件销毁时关闭连接
    if (this.currentEventSource) this.currentEventSource.close()
    // 清理语音相关资源
    this.cleanupVoice()
    // 停止TTS
    this.stopTTS()
  }
}
</script>

<style scoped>
.chat-window {
  width: 100%;
  max-width: 984px;
  margin: 0 auto;
  position: relative;
}

/* 右上角语言选择器样式 */
.language-selector {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.language-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin: 0;
}

.global-lang-select {
  min-width: 100px;
  height: 28px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.global-lang-select:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.global-lang-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.chat-container {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  min-height: auto;
  overflow: visible;
  display: flex;
  flex-direction: column;
}







@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}



.chat-input-container {
  width: 100%;
  max-width: 600px;
  margin: 20px auto 0;
}

/* 选择错误提示样式 */
.selection-error {
  background: #ffebee;
  border: 1px solid #f44336;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  text-align: center;
  color: #c62828;
  font-size: 14px;
  font-weight: 500;
  animation: fadeInError 0.3s ease-out;
}

.selection-error span {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.selection-error::before {
  content: '⚠️';
  font-size: 16px;
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-wrapper {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  padding: 20px 24px;
  display: flex;
  align-items: flex-end;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  min-height: 70px;
  position: relative;
}

.image-preview {
  position: relative;
  flex-shrink: 0;
}

.image-preview img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrapper textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  font-family: inherit;
  min-height: 24px;
  max-height: 120px;
  color: #333;
}

.input-wrapper textarea::placeholder {
  color: #a4a4a4;
  font-size: 16px;
}

/* 功能按钮组样式 */
.input-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-right: 8px;
}

/* 输入框切换按钮样式 */
.input-toggle-container {
  width: 100%;
  max-width: 600px;
  margin: 20px auto 0;
  text-align: center;
}

.input-toggle-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.input-toggle-btn:hover {
  background: #0056b3;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #a4a4a4;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #666;
}

.action-btn.voice-btn.active {
  background: #ff4444;
  color: white;
  animation: pulse 1s infinite;
}

/* 语音播报按钮激活样式 */
.action-btn.tts-btn.active {
  background: #e8fff3;
  color: #0f9d58;
}

.lang-select {
  height: 28px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fff;
  color: #333;
  padding: 0 8px;
  font-size: 12px;
}

/* 语音输入面板 Overlay */
.voice-overlay {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  pointer-events: none;
  z-index: 2000;
  animation: slideUpIn 0.3s ease-out;
}

@keyframes slideUpIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.voice-panel {
  margin: 16px;
  width: min(580px, calc(100% - 32px));
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 
    0 20px 40px rgba(0,0,0,0.15),
    0 8px 16px rgba(0,0,0,0.10),
    0 0 0 1px rgba(255,255,255,0.8);
  backdrop-filter: blur(20px);
  pointer-events: auto;
  z-index: 2001;
  overflow: hidden;
}

.voice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0,0,0,0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.voice-title {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-close {
  border: none;
  background: rgba(255,255,255,0.1);
  color: white;
  cursor: pointer;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-close:hover {
  background: rgba(255,255,255,0.2);
  transform: scale(1.05);
}

.voice-main {
  padding: 24px 20px;
}

/* 音频波形动画 */
.voice-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;
}

.wave-container {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 60px;
  justify-content: center;
}

.wave-bar {
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: all 0.2s ease;
  opacity: 0.3;
}

.wave-container.active .wave-bar {
  opacity: 1;
  animation: waveMove 1.2s ease-in-out infinite;
}

@keyframes waveMove {
  0%, 100% { 
    transform: scaleY(0.2); 
  }
  50% { 
    transform: scaleY(1); 
  }
}

/* 状态图标 */
.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.recording-pulse {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-core {
  width: 16px;
  height: 16px;
  background: #ff4444;
  border-radius: 50%;
  z-index: 2;
}

.pulse-ring {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #ff4444;
  border-radius: 50%;
  animation: pulseRing 1.5s ease-out infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.processing-spinner {
  width: 40px;
  height: 40px;
}

.spinner-svg {
  width: 100%;
  height: 100%;
  animation: spin 1s linear infinite;
}

@keyframes spin { 
  from { transform: rotate(0deg); } 
  to { transform: rotate(360deg); } 
}

.spinner-circle {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: spinStroke 1.5s ease-in-out infinite;
}

@keyframes spinStroke {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.standby-icon {
  font-size: 32px;
  opacity: 0.6;
}

/* 状态文字区域 */
.voice-status-section {
  text-align: center;
  margin-bottom: 20px;
}

.voice-status-text {
  font-size: 18px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8px;
}

.voice-hint {
  font-size: 14px;
  color: #718096;
}

/* 音量可视化 */
.volume-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 0 8px;
}

.volume-label {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
  min-width: 40px;
}

.volume-meter {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.volume-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.1s ease, background 0.2s ease;
  position: relative;
}

.volume-bar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: rgba(255,255,255,0.3);
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* 控制区域 */
.voice-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(0,0,0,0.06);
  background: rgba(247,250,252,0.5);
}

.voice-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.lang-select {
  height: 36px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #2d3748;
  padding: 0 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.lang-select:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.lang-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.stop-voice-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 10px;
  border: none;
  background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
  color: white;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(255,68,68,0.25);
}

.stop-voice-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff3333 0%, #bb2222 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(255,68,68,0.35);
}

.stop-voice-btn:disabled {
  background: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-selector {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 6px 8px;
    border-radius: 6px;
  }
  
  .language-label {
    display: none;
  }
  
  .global-lang-select {
    min-width: 80px;
    height: 24px;
    font-size: 11px;
  }
}

@media (max-width: 640px) {
  .voice-panel {
    margin: 12px;
    width: calc(100% - 24px);
    border-radius: 16px;
  }
  
  .voice-main {
    padding: 20px 16px;
  }
  
  .wave-container {
    height: 50px;
  }
  
  .voice-status-text {
    font-size: 16px;
  }
  
  .voice-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .lang-select, .stop-voice-btn {
    width: 100%;
    justify-content: center;
  }
}

.send-btn {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #e0e0e0;
  border: none;
  border-radius: 12px;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-btn.can-send {
  background: #007bff;
  color: white;
}

.send-btn:hover.can-send {
  background: #0056b3;
}

.send-btn.loading {
  background: #6c757d;
  cursor: not-allowed;
  animation: pulse 1s infinite;
}

/* 清空对话（输入框右侧） */
.clear-chat-inline-btn {
  flex-shrink: 0;
  height: 40px;
  padding: 0 14px;
  border: none;
  border-radius: 10px;
  background: #f58147;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(245, 129, 71, 0.25);
}

.clear-chat-inline-btn:hover {
  background: #e06a2e;
  transform: translateY(-1px);
}





.messages-list {
  margin-bottom: 20px;
  overflow-y: auto;
  max-height: 400px;
  padding-right: 8px;
  transition: all 0.3s ease;
}

/* 当输入框隐藏时，消息列表占据更多空间 */
.messages-list.expanded {
  max-height: calc(100vh - 150px);
  margin-bottom: 20px;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.ai-avatar,
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.ai-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar {
  background: #007bff;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-bubble {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: #007bff;
  color: white;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}



.message-text {
  font-size: 14px;
  line-height: 1.5;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 时间 + 喇叭按钮容器 */
.message-meta {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.tts-inline-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 22px;
  padding: 0;
  border: 1px solid rgba(0,0,0,0.1);
  border-radius: 6px;
  background: #f7f7f7;
  color: #444;
  cursor: pointer;
}

.tts-inline-btn:hover {
  background: #eef7f2;
  color: #0f9d58;
}

/* 流式响应指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #007bff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动条样式 */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    padding: 16px;
    padding-top: 50px; /* 给语言选择器留出空间 */
  }
  
  .chat-input-container {
    width: calc(100% - 32px);
    margin: 16px auto 0;
  }
  
  .input-toggle-container {
    width: calc(100% - 32px);
    margin: 16px auto 0;
  }
  
  .messages-list {
    max-height: 300px;
  }
  
  .messages-list.expanded {
    max-height: calc(100vh - 250px); /* 调整高度以适应语言选择器 */
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>
