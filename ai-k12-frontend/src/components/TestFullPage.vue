<template>
  <div id="fullpage">
    <div class="section" style="background-color: #ff5f56;">
      <h1>Section 1</h1>
      <p>This is the first section</p>
    </div>
    <div class="section" style="background-color: #ffbd2e;">
      <h1>Section 2</h1>
      <p>This is the second section</p>
    </div>
    <div class="section" style="background-color: #27ca3f;">
      <h1>Section 3</h1>
      <p>This is the third section</p>
    </div>
    <div class="section" style="background-color: #1890ff;">
      <h1>Section 4</h1>
      <p>This is the fourth section</p>
    </div>
  </div>
</template>

<script>
import fullpage from 'fullpage.js'
import 'fullpage.js/dist/fullpage.min.css'

export default {
  name: 'TestFullPage',
  mounted() {
    this.$nextTick(() => {
      console.log('Initializing test fullPage.js...')
      const fp = new fullpage('#fullpage', {
        navigation: true,
        navigationPosition: 'right',
        navigationTooltips: ['Section 1', 'Section 2', 'Section 3', 'Section 4'],
        showActiveTooltip: true,
        scrollingSpeed: 700,
        autoScrolling: true,
        fitToSection: true,
        keyboardScrolling: true,
        verticalCentered: true,
        sectionSelector: '.section',
        credits: { enabled: false },
        afterLoad: function(origin, destination, direction) {
          console.log('Loaded section:', destination.index + 1)
        }
      })
      console.log('Test fullPage.js initialized:', fp)
    })
  },
  beforeUnmount() {
    if (window.fullpage_api) {
      window.fullpage_api.destroy('all')
    }
  }
}
</script>

<style scoped>
.section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  font-family: Arial, sans-serif;
}

.section h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.section p {
  font-size: 1.5rem;
}
</style>
