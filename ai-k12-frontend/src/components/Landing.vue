<template>
  <div>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="navbar-left">
        <div class="logo">
          <img src="/images/logo1.png" alt="Thinky Logo" class="logo-image" />
        </div>
        
        <div class="nav-menu">
          <a href="#tools" class="nav-item">
            Product
            <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 10px;"></i>
          </a>
          <a href="#track" class="nav-item">
            About us
            <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 10px;"></i>
          </a>
        </div>
      </div>

      <div class="navbar-right">
        <a href="javascript:void(0)" @click.prevent="goHome" class="cta-button">Start For Free</a>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="hero">
      <div class="hero-overlay">
        <h1 class="hero-overlay-title">Beyond the Answer, Towards Thinking</h1>
        <p class="hero-overlay-subtitle">An AI tutor that teaches how to think, not just answer.</p>
          </div>
       <img src="/images/learner.png" alt="Learner" class="hero-learner" />
       <img src="/images/hero.png" alt="Hero" class="hero-full" />

       <!-- Philosophy Banner -->
       <div class="hero-philosophy">
         <h2 class="hero-philosophy-title">Thinky Learning Philosophy</h2>
         <p class="hero-philosophy-subtitle">Answers are not the destination, thinking is the true beginning</p>
              </div>
              

    </section>

    <!-- Talk Section -->
    <section class="feature-section talk-section" id="talk">
      <!-- Feature Navigation Pills - positioned between hero and talk -->
      <div class="feature-navigation-pills">
        <div class="nav-container">
          <a href="#talk" class="nav-pill active" data-section="talk">
            <img src="/images/nav-talk.svg" alt="Talk" class="nav-pill-image" 
                 data-normal="/images/nav-talk.svg" data-active="/images/nav-talk-active.svg">
          </a>
          <a href="#think" class="nav-pill" data-section="think">
            <img src="/images/nav-think.svg" alt="Think" class="nav-pill-image"
                 data-normal="/images/nav-think.svg" data-active="/images/nav-think-active.svg">
          </a>
          <a href="#teach" class="nav-pill" data-section="teach">
            <img src="/images/nav-teach.svg" alt="Teach" class="nav-pill-image"
                 data-normal="/images/nav-teach.svg" data-active="/images/nav-teach-active.svg">
          </a>
          <a href="#test" class="nav-pill" data-section="test">
            <img src="/images/nav-test.svg" alt="Test" class="nav-pill-image"
                 data-normal="/images/nav-test.svg" data-active="/images/nav-test-active.svg">
          </a>
          <a href="#track" class="nav-pill" data-section="track">
            <img src="/images/nav-track.svg" alt="Track" class="nav-pill-image"
                 data-normal="/images/nav-track.svg" data-active="/images/nav-track-active.svg">
          </a>
        </div>
      </div>
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up">
          <img class="talk-prototype-image" src="/images/thinky-talk-prototype2.png" alt="Talk section prototype" />
        </div>
        <div class="feature-content animate-fade-in-up">
          <h2>Talk</h2>
          <h3 class="feature-subtitle">Learn through dialogue</h3>
          <p class="feature-description">Smooth conversations that feel like a real teacher guiding you.</p>
        </div>
      </div>
    </section>

    <!-- Menu Image Between Talk and Think -->
    <section class="menu-section">
      <div class="menu-image-container">
        <img src="/images/menu2.png" alt="Navigation Menu" class="menu-divider-image" />
        <div class="menu-text-overlay">
          <h2 class="menu-title">Thinky Learning Philosophy</h2>
          <p class="menu-subtitle">Answers are not the destination, thinking is the true beginning</p>
        </div>
      </div>
    </section>

    <!-- Think Section -->
    <section class="feature-section think-section" id="think">
      <!-- Feature Navigation Pills - positioned between menu and think -->
      <div class="feature-navigation-pills">
        <div class="nav-container">
          <a href="#talk" class="nav-pill active" data-section="talk">
            <img src="/images/nav-talk.svg" alt="Talk" class="nav-pill-image" 
                 data-normal="/images/nav-talk.svg" data-active="/images/nav-talk-active.svg">
          </a>
          <a href="#think" class="nav-pill" data-section="think">
            <img src="/images/nav-think.svg" alt="Think" class="nav-pill-image"
                 data-normal="/images/nav-think.svg" data-active="/images/nav-think-active.svg">
          </a>
          <a href="#teach" class="nav-pill" data-section="teach">
            <img src="/images/nav-teach.svg" alt="Teach" class="nav-pill-image"
                 data-normal="/images/nav-teach.svg" data-active="/images/nav-teach-active.svg">
          </a>
          <a href="#test" class="nav-pill" data-section="test">
            <img src="/images/nav-test.svg" alt="Test" class="nav-pill-image"
                 data-normal="/images/nav-test.svg" data-active="/images/nav-test-active.svg">
          </a>
          <a href="#track" class="nav-pill" data-section="track">
            <img src="/images/nav-track.svg" alt="Track" class="nav-pill-image"
                 data-normal="/images/nav-track.svg" data-active="/images/nav-track-active.svg">
          </a>
        </div>
      </div>
      <div class="section-container">
        <div class="feature-content animate-fade-in-up">
          <h2>Think</h2>
          <h3 class="feature-subtitle">Think before the answer</h3>
          <p class="feature-description">Guided steps that build reasoning skills, not just quick solutions.</p>
        </div>
        <div class="feature-visual animate-fade-in-up">
          <img class="think-prototype-image" src="/images/thinky-think-prototype.png" alt="Think section prototype" />
        </div>
      </div>
    </section>

    <!-- Menu Image Between Think and Teach -->
    <section class="menu-section">
      <div class="menu-image-container">
        <img src="/images/menu2.png" alt="Navigation Menu" class="menu-divider-image" />
        <div class="menu-text-overlay">
          <h2 class="menu-title">Thinky Learning Philosophy</h2>
          <p class="menu-subtitle">Answers are not the destination, thinking is the true beginning</p>
        </div>
      </div>
    </section>

    <!-- Teach Section -->
    <section class="feature-section teach-section" id="teach">
      <!-- Feature Navigation Pills - positioned between menu and teach -->
      <div class="feature-navigation-pills">
        <div class="nav-container">
          <a href="#talk" class="nav-pill active" data-section="talk">
            <img src="/images/nav-talk.svg" alt="Talk" class="nav-pill-image" 
                 data-normal="/images/nav-talk.svg" data-active="/images/nav-talk-active.svg">
          </a>
          <a href="#think" class="nav-pill" data-section="think">
            <img src="/images/nav-think.svg" alt="Think" class="nav-pill-image"
                 data-normal="/images/nav-think.svg" data-active="/images/nav-think-active.svg">
          </a>
          <a href="#teach" class="nav-pill" data-section="teach">
            <img src="/images/nav-teach.svg" alt="Teach" class="nav-pill-image"
                 data-normal="/images/nav-teach.svg" data-active="/images/nav-teach-active.svg">
          </a>
          <a href="#test" class="nav-pill" data-section="test">
            <img src="/images/nav-test.svg" alt="Test" class="nav-pill-image"
                 data-normal="/images/nav-test.svg" data-active="/images/nav-test-active.svg">
          </a>
          <a href="#track" class="nav-pill" data-section="track">
            <img src="/images/nav-track.svg" alt="Track" class="nav-pill-image"
                 data-normal="/images/nav-track.svg" data-active="/images/nav-track-active.svg">
          </a>
        </div>
      </div>
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up"><div class="teach-graph-visual"></div></div>
        <div class="feature-content animate-fade-in-up">
          <h2>Teach</h2>
          <h3 class="feature-subtitle">Turn complexity into clarity</h3>
          <p class="feature-description">Break down tough concepts into simple, structured knowledge.</p>
        </div>
      </div>
    </section>

    <!-- Menu Image Between Teach and Test -->
    <section class="menu-section">
      <div class="menu-image-container">
        <img src="/images/menu2.png" alt="Navigation Menu" class="menu-divider-image" />
        <div class="menu-text-overlay">
          <h2 class="menu-title">Thinky Learning Philosophy</h2>
          <p class="menu-subtitle">Answers are not the destination, thinking is the true beginning</p>
        </div>
      </div>
    </section>

    <!-- Test Section -->
    <section class="feature-section test-section" id="test">
      <!-- Feature Navigation Pills - positioned between menu and test -->
      <div class="feature-navigation-pills">
        <div class="nav-container">
          <a href="#talk" class="nav-pill active" data-section="talk">
            <img src="/images/nav-talk.svg" alt="Talk" class="nav-pill-image" 
                 data-normal="/images/nav-talk.svg" data-active="/images/nav-talk-active.svg">
          </a>
          <a href="#think" class="nav-pill" data-section="think">
            <img src="/images/nav-think.svg" alt="Think" class="nav-pill-image"
                 data-normal="/images/nav-think.svg" data-active="/images/nav-think-active.svg">
          </a>
          <a href="#teach" class="nav-pill" data-section="teach">
            <img src="/images/nav-teach.svg" alt="Teach" class="nav-pill-image"
                 data-normal="/images/nav-teach.svg" data-active="/images/nav-teach-active.svg">
          </a>
          <a href="#test" class="nav-pill" data-section="test">
            <img src="/images/nav-test.svg" alt="Test" class="nav-pill-image"
                 data-normal="/images/nav-test.svg" data-active="/images/nav-test-active.svg">
          </a>
          <a href="#track" class="nav-pill" data-section="track">
            <img src="/images/nav-track.svg" alt="Track" class="nav-pill-image"
                 data-normal="/images/nav-track.svg" data-active="/images/nav-track-active.svg">
          </a>
        </div>
      </div>
      <div class="section-container">
        <div class="feature-content animate-fade-in-up">
          <h2>Test</h2>
          <h3 class="feature-subtitle">Spot weaknesses fast</h3>
          <p class="feature-description">Adaptive quizzes reveal blind spots and direct your practice.</p>
        </div>
        <div class="feature-visual animate-fade-in-up">
          <img class="test-prototype-image" src="/images/thinky-test-prototype.png" alt="Test section prototype" />
        </div>
      </div>
    </section>

    <!-- Menu Image Between Test and Track -->
    <section class="menu-section">
      <div class="menu-image-container">
        <img src="/images/menu2.png" alt="Navigation Menu" class="menu-divider-image" />
        <div class="menu-text-overlay">
          <h2 class="menu-title">Thinky Learning Philosophy</h2>
          <p class="menu-subtitle">Answers are not the destination, thinking is the true beginning</p>
        </div>
      </div>
    </section>

    <!-- Track Section (use prototype image on the left) -->
    <section class="feature-section track-section" id="track">
      <!-- Feature Navigation Pills - positioned between menu and track -->
      <div class="feature-navigation-pills">
        <div class="nav-container">
          <a href="#talk" class="nav-pill active" data-section="talk">
            <img src="/images/nav-talk.svg" alt="Talk" class="nav-pill-image" 
                 data-normal="/images/nav-talk.svg" data-active="/images/nav-talk-active.svg">
          </a>
          <a href="#think" class="nav-pill" data-section="think">
            <img src="/images/nav-think.svg" alt="Think" class="nav-pill-image"
                 data-normal="/images/nav-think.svg" data-active="/images/nav-think-active.svg">
          </a>
          <a href="#teach" class="nav-pill" data-section="teach">
            <img src="/images/nav-teach.svg" alt="Teach" class="nav-pill-image"
                 data-normal="/images/nav-teach.svg" data-active="/images/nav-teach-active.svg">
          </a>
          <a href="#test" class="nav-pill" data-section="test">
            <img src="/images/nav-test.svg" alt="Test" class="nav-pill-image"
                 data-normal="/images/nav-test.svg" data-active="/images/nav-test-active.svg">
          </a>
          <a href="#track" class="nav-pill" data-section="track">
            <img src="/images/nav-track.svg" alt="Track" class="nav-pill-image"
                 data-normal="/images/nav-track.svg" data-active="/images/nav-track-active.svg">
          </a>
        </div>
      </div>
      <div class="section-container">
        <div class="feature-visual animate-fade-in-up">
          <img class="track-prototype-image" src="/images/thinky-track-prototype.png" alt="Track section prototype" />
        </div>
        <div class="feature-content animate-fade-in-up">
          <h2>Track</h2>
          <h3 class="feature-subtitle" style="color:#111827">See your growth</h3>
          <p class="feature-description">Knowledge graphs and insights show progress over time.</p>
        </div>
      </div>
    </section>

    <!-- Study Tool Suite (two-column) -->
    <section class="study-tools" id="tools">
      <div class="study-suite">
        <div class="suite-left">
          <h2 class="suite-heading">Study Tool Suite</h2>
          <p class="suite-subtitle">A set of AI tools to make learning <span class="highlight">faster</span> and <span class="highlight">easier</span>.</p>
          <img class="suite-illustration" src="/images/StudyToolSuite.png" alt="Study Tool Suite illustration" />
        </div>
        <div class="suite-right">
          <div class="suite-list">
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-clipboard"></i></div><div><h4>Note-Taking AI</h4><p>Instantly generate notes and outlines from course materials.</p></div></div>
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-file-alt"></i></div><div><h4>Exam Simulation AI</h4><p>Automatically create mock exams and simulate real test environments.</p></div></div>
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-circle-xmark"></i></div><div><h4>Error Bank AI</h4><p>Automatically organizes mistakes, supports targeted practice and review.</p></div></div>
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-video"></i></div><div><h4>Video Explainer AI</h4><p>Turn knowledge points into short explainer videos or animations.</p></div></div>
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-clone"></i></div><div><h4>Flashcard AI</h4><p>One‑click flashcard generation for effective memory and review.</p></div></div>
            <div class="suite-item animate-fade-in-up"><div class="suite-badge"><i class="fas fa-calendar-alt"></i></div><div><h4>Study Calendar AI</h4><p>Intelligently schedules study time, generates study plans and reminders.</p></div></div>
          </div>
        </div>
      </div>
    </section>
    <!-- Keep other sections from the original as needed later -->

    <!-- CTA Section (bottom) -->
    <section class="cta-section" id="cta">
      <div class="cta-container">
        <div class="cta-left">
          <h2>Ready to ace the study ?</h2>
          <p class="cta-subtitle">Sign up to revolutionise your learning !</p>
          <a href="javascript:void(0)" @click.prevent="goHome" class="cta-button-large">Start For Free</a>
        </div>
        <div class="cta-right">
          <div class="character-bottom" />
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-left">
            <div class="footer-brand">
              <img src="/images/logo2.png" alt="Thinky Logo" class="logo-image" />
            </div>
            <p class="footer-tagline">Learning Beyond the Answer</p>
            <div class="social-links">
              <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
              <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
              <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
            </div>
          </div>
          <div class="download-section">
            <h3>Download</h3>
            <p class="download-description">Anytime, anywhere, download to use Thinky AI.</p>
            <div class="download-buttons">
              <a href="#" class="download-button">
                <i class="fab fa-apple"></i>
                <div class="download-info">
                  <div class="download-type">Download</div>
                  <div class="download-store">App Store</div>
                </div>
              </a>
              <a href="#" class="download-button">
                <i class="fab fa-google-play"></i>
                <div class="download-info">
                  <div class="download-type">Download</div>
                  <div class="download-store">Google Play</div>
                </div>
              </a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p class="copyright">© 2025 Thinky AI . All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
  
</template>

<script>
export default {
  name: 'Landing',
  mounted() {
    // Smooth scroll for in-page anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        const href = anchor.getAttribute('href')
        if (!href || href === '#' || href.startsWith('/')) return
        e.preventDefault()
        const target = document.querySelector(href)
        if (target) {
          const navbar = document.querySelector('.navbar')
          const navbarHeight = navbar ? navbar.offsetHeight : 0
          const extraSpacing = 20 // breathing room
          const offsetTop = target.offsetTop - (navbarHeight + extraSpacing)
          window.scrollTo({ top: Math.max(0, offsetTop), behavior: 'smooth' })
        }
      })
    })

    // Feature navigation active state + image swap
    const sections = ['talk', 'think', 'teach', 'test', 'track']
    const navPills = Array.from(document.querySelectorAll('.nav-pill'))

    const refreshNavImages = () => {
      navPills.forEach(pill => {
        const img = pill.querySelector('img')
        if (!img) return
        const normal = img.getAttribute('data-normal')
        const active = img.getAttribute('data-active') || normal
        img.src = pill.classList.contains('active') ? active : normal
      })
    }

    const setActiveByIndex = (activeIndex) => {
      const groupSize = sections.length
      navPills.forEach((pill, i) => {
        const idxInGroup = i % groupSize
        pill.classList.toggle('active', idxInGroup === activeIndex)
      })
      refreshNavImages()
    }

    const updateActiveFromScroll = () => {
      const scrollPosition = window.scrollY + 200

      // If we're in a menu-section (between sections), activate the NEXT section
      const menuSections = Array.from(document.querySelectorAll('.menu-section'))
      for (let i = 0; i < menuSections.length; i++) {
        const ms = menuSections[i]
        const top = ms.offsetTop
        const bottom = top + ms.offsetHeight
        if (scrollPosition >= top && scrollPosition < bottom) {
          const nextIndex = Math.min(i + 1, sections.length - 1)
          setActiveByIndex(nextIndex)
          return
        }
      }

      // Otherwise, activate the section we're currently in
      let currentIndex = 0
      sections.forEach((id, index) => {
        const el = document.getElementById(id)
        if (!el) return
        const top = el.offsetTop
        const bottom = top + el.offsetHeight
        if (scrollPosition >= top && scrollPosition < bottom) {
          currentIndex = index
        }
      })
      setActiveByIndex(currentIndex)
    }

    // Initialize active state on load
    updateActiveFromScroll()

    // Update on scroll
    window.addEventListener('scroll', updateActiveFromScroll)

    // Handle navigation pill clicks
    navPills.forEach(pill => {
      pill.addEventListener('click', (e) => {
        e.preventDefault()
        const targetId = pill.getAttribute('data-section')
        const targetElement = document.getElementById(targetId)
        
        if (targetElement) {
          const navbar = document.querySelector('.navbar')
          const navbarHeight = navbar ? navbar.offsetHeight : 0
          const offsetTop = targetElement.offsetTop - navbarHeight - 20
          
          window.scrollTo({
            top: Math.max(0, offsetTop),
            behavior: 'smooth'
          })
        }
      })
    })

    // Appear animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => { if (entry.isIntersecting) entry.target.classList.add('animate-fade-in-up') })
    }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' })
    document.querySelectorAll('section, .suite-item, .feature-content, .feature-visual').forEach(el => observer.observe(el))

    // Navbar: show only until passing the learner image (robust to layout)
    const navbar = document.querySelector('.navbar')
    const heroSection = document.querySelector('.hero')
    const learnerImage = document.querySelector('.hero-learner')

    let navbarHideThreshold = 0
    const computeNavbarThreshold = () => {
      if (learnerImage) {
        const rect = learnerImage.getBoundingClientRect()
        navbarHideThreshold = rect.bottom + window.scrollY - 1
      } else if (heroSection) {
        const rect = heroSection.getBoundingClientRect()
        navbarHideThreshold = rect.bottom + window.scrollY - 1
      } else {
        navbarHideThreshold = 0
      }
    }

    const setNavbarVisibility = () => {
      if (!navbar) return
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const beforeThreshold = scrollTop < navbarHideThreshold
      navbar.style.opacity = beforeThreshold ? '1' : '0'
      navbar.style.pointerEvents = beforeThreshold ? 'auto' : 'none'
      navbar.style.transform = beforeThreshold ? 'translateY(0)' : 'translateY(-100%)'
    }

    computeNavbarThreshold()
    setNavbarVisibility()
    window.addEventListener('scroll', setNavbarVisibility)
    window.addEventListener('resize', () => { computeNavbarThreshold(); setNavbarVisibility() })
  },
  methods: {
    goHome() {
      this.$router.push('/home')
    }
  }
}
</script>

<style>
/* Core reset for this view */
* { margin: 0; padding: 0; box-sizing: border-box; }
body { overflow-x: hidden; }

/* Navigation */
.navbar { position: fixed; top:0; left:0; right:0; background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-bottom: 1px solid #E5E7EB; padding: 18px; display:flex; justify-content:space-between; align-items:center; z-index:1000; height: 69px; transform: translateY(0); transition: transform 0.3s ease; }
.navbar-left { display: flex; align-items: center; gap: 40px; }
.navbar-right { display: flex; align-items: center; }
.logo { display:flex; align-items:center; }
.logo-image { height: 48px; width: auto; object-fit: contain; }
.nav-menu { display:flex; align-items:center; gap: 4px; }
.nav-item { padding: 7px 18px; border-radius: 6px; color:#3C315B; font-weight:500; font-size:14px; text-decoration:none; }
.cta-button { background:#3F51B5; color:#fff; padding:8px 15px; border-radius: 9999px; text-decoration:none; font-weight:500; font-size:14px; }

/* Hero Section */
.hero { 
  position: relative; 
  background: #ffffff; 
  padding: 69px 0 0; 
  overflow: hidden; 
  min-height: auto;
}
.hero-full { display:block; width:100%; height:auto; }
.hero-learner { position:absolute; left:50%; transform: translateX(-50%); top: 180px; width: 420px; height: auto; z-index:2; pointer-events:none; }
.hero-overlay { position:absolute; left:0; right:0; top:100px; text-align:center; z-index: 3; pointer-events: none; padding: 0 20px; }
.hero-overlay-title { font-family: 'Inter', sans-serif; font-weight: 800; font-size: clamp(32px, 4vw, 56px); color: #33439B; margin-bottom: 16px; }
.hero-overlay-subtitle { font-family: 'Inter', sans-serif; font-weight: 500; font-size: clamp(16px, 2.2vw, 28px); color: #33439B; opacity: 0.95; }


/* Feature Navigation Pills - positioned between sections */
.feature-navigation-pills {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
}

.nav-container {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 0 20px;
  pointer-events: auto;
}

.nav-pill {
  display: inline-block;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 8px;
}

.nav-pill:hover {
  transform: translateY(-2px);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

.nav-pill.active {
  transform: translateY(-1px);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.nav-pill-image {
  display: block;
  width: auto;
  height: 60px;
  transition: all 0.3s ease;
}

/* Talk section */
.talk-section { 
  padding: 120px 0; 
  background: #FFFFFF; 
}

/* Menu Section Between Talk and Think */
.menu-section {
  background: #FFFFFF;
  padding: 0;
  position: relative;
  width: 100%;
}

.menu-image-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-divider-image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  min-height: 200px;
}

.menu-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #FFFFFF;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  padding: 0 20px;
}

.menu-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: clamp(28px, 4vw, 48px);
  line-height: 1.2;
  margin-bottom: 12px;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.menu-subtitle {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(14px, 2vw, 20px);
  opacity: 0.95;
  line-height: 1.4;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Position navigation pills at the top of talk section */
.feature-navigation-pills {
  top: -30px; /* Half of the pill height to center on the border */
}

.talk-prototype-image { display:block; width:100%; height:auto; }

/* Menu image under block 1 */
.menu-image-wrapper { max-width: 1000px; margin: 20px auto 0; padding: 0 20px; }
.menu-image { display:block; width:100%; height:auto; }

/* Philosophy banner under hero */
.hero-philosophy { 
  color: #FFFFFF; 
  text-align: center; 
  padding: 40px 20px; 
  position: absolute; 
  left: 0; 
  right: 0; 
  bottom: 0; 
  z-index: 4; 
  width: 100%;
  pointer-events: none;
}
.hero-philosophy-title { 
  font-family: 'Inter', sans-serif; 
  font-weight: 700; 
  font-size: clamp(32px, 4vw, 48px); 
  line-height: 1.2; 
  margin-bottom: 12px; 
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.hero-philosophy-subtitle { 
  font-family: 'Inter', sans-serif; 
  font-weight: 400; 
  font-size: clamp(16px, 2vw, 20px); 
  opacity: 0.95; 
  line-height: 1.4; 
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}


/* Shared feature section layout */
.feature-section { padding: 120px 0; position: relative; background: #FFFFFF; }
.section-container { max-width: 1280px; margin: 0 auto; padding: 0 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 100px; align-items: center; }
.feature-content h2 { font-family: 'PingFang SC', sans-serif; font-weight: 600; font-size: clamp(60px, 8vw, 118.7px); color: #535353; margin-bottom: 30px; line-height: 1; }
.feature-subtitle { font-family: 'PingFang SC', sans-serif; font-weight: 600; font-size: clamp(24px, 3vw, 34.06px); color: #000; margin-bottom: 20px; }
.feature-description { font-family: 'PingFang SC', sans-serif; font-weight: 500; font-size: clamp(16px, 2vw, 25.54px); color: #535353; line-height: 1.43; }
.feature-strong { font-family: 'Inter', sans-serif; font-weight: 800; font-size: clamp(20px, 3vw, 34px); color: #4B5563; margin-top: 16px; }
.feature-visual { position: relative; min-height: 300px; display:flex; align-items:center; justify-content:center; }

/* Think section */
.think-section { 
  background: #E3E9FA; 
  position: relative;
}
.think-section h2 { color: #535353; }
.think-section .feature-subtitle, .think-section .feature-description { color: #2F3348; }

/* Teach */
.teach-section { position: relative; }
.teach-graph-visual { background: url('/images/math-chart.png') left center/contain no-repeat; width:100%; height:460px; }
.teach-section .section-container { grid-template-columns: 1.3fr 1fr; gap: 80px; }

/* Test */
.test-section { 
  background: #3F51B5; 
  color: #fff; 
  position: relative;
}
.test-section h2, .test-section .feature-subtitle, .test-section .feature-description { color: #fff; }

/* Track */
.track-section { position: relative; }

/* Track (cards illustration) */
.track-visual { position: relative; height: 420px; }
.track-canvas { position: relative; width: 100%; height: 100%; }
.track-card { position: absolute; background: #FFFFFF; border: 2px solid #5875FF; border-radius: 22px; box-shadow: 0 16px 36px rgba(76,111,255,0.16); }
.track-detections { left: 5%; top: 6%; width: 56%; padding: 16px 18px 10px; }
.det-pie { position: absolute; left: -32px; top: 16px; width: 74px; height: 74px; border-radius: 50%; background: #FFFFFF; border: 2px solid #5875FF; box-shadow: 0 10px 24px rgba(76,111,255,0.16); display:flex; align-items:center; justify-content:center; font-size: 11px; color:#5875FF; text-align:center; line-height:1.1; }
.det-wrapper { margin-left: 60px; }
.det-card { background: #FFFFFF; border: 1.5px solid #C8D8FF; border-radius: 16px; padding: 14px 16px; margin-bottom: 12px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); }
.det-title { display: inline-flex; align-items: center; gap: 8px; font-weight: 800; font-size: 14px; color: #2E61E6; margin-bottom: 10px; }
.det-bullet { width: 10px; height: 10px; border-radius: 50%; }
.det-row { display: flex; align-items: center; gap: 10px; margin-top: 6px; }
.star { width: 10px; height: 10px; transform: rotate(45deg); background: linear-gradient(180deg, #FFD277 0%, #FFB347 100%); border-radius: 2px; display: inline-block; }
.slider-pill { position: absolute; right: -26px; top: 58px; background: #EAF1FF; border: 2px solid #6B8BFF; border-radius: 999px; padding: 8px 18px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); display:flex; gap:10px; }
.slider-dot { width: 12px; height: 12px; border-radius: 50%; background: #6B8BFF; box-shadow: inset 0 0 0 3px #FFFFFF; }
.track-chart { left: 3%; bottom: 4%; width: 62%; padding: 18px 20px 20px; }
.chart-header-pill { position: absolute; top: -16px; left: 40px; background: #EAF1FF; border: 2px solid #6B8BFF; color: #6B8BFF; padding: 8px 22px; border-radius: 999px; font-size: 12px; box-shadow: 0 10px 20px rgba(76,111,255,0.12); }
.gauge { width: 96px; height: 96px; border: 2px dashed #CAD7FF; border-radius: 50%; position: relative; }
.gauge::after { content: ''; position: absolute; width: 58px; height: 58px; border-top: 6px solid #4C6FFF; border-right: 6px solid transparent; border-radius: 50%; transform: rotate(-20deg); top: 14px; left: 14px; }
.info-lines { flex:1; padding-top: 6px; }
.mini-line { height: 4px; background: linear-gradient(90deg, #6B8BFF, #46C2C2); border-radius: 6px; margin: 8px 0; opacity: .9; }
.bars { display: flex; align-items: end; gap: 12px; padding: 12px 8px 0; }
.bar { width: 24px; background: #10B2A8; border-radius: 6px; }
.bar:nth-child(1){ height: 44px; }
.bar:nth-child(2){ height: 64px; }
.bar:nth-child(3){ height: 40px; }
.bar:nth-child(4){ height: 78px; }
.chart-star { position: absolute; right: 22px; top: -12px; width: 14px; height: 14px; transform: rotate(45deg); background: linear-gradient(180deg, #FFD277 0%, #FFB347 100%); border-radius: 2px; }

/* When using a static image for Track visual */
.track-prototype-image { display:block; width:100%; height:auto; }

/* Test prototype image */
.test-prototype-image { display:block; width:100%; height:auto; }

/* Think prototype image */
.think-prototype-image { display:block; width:100%; height:auto; }

/* Study tools two-column */
.study-tools { background: #E3E9FA; padding: 120px 0; }
.study-suite { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: grid; grid-template-columns: 1.2fr 1fr; gap: 60px; align-items: start; }
.suite-left { display: flex; flex-direction: column; gap: 16px; }
.suite-heading { font-family: 'Inter', sans-serif; font-weight: 800; letter-spacing: -0.02em; font-size: clamp(40px, 6vw, 64px); color: #53575F; }
.suite-subtitle { font-family: 'Inter', sans-serif; font-weight: 400; font-size: clamp(16px, 2vw, 22px); color: #5B6279; line-height: 1.6; }
.suite-subtitle .highlight { background: linear-gradient(90deg, #4C6FFF 0%, #06B6D4 100%); -webkit-background-clip: text; background-clip: text; color: transparent; font-weight: 700; }
.suite-illustration { margin-top: 22px; margin-left: -20px; width: 100%; height: 340px; border-radius: 18px; box-shadow: 0 24px 60px rgba(76,111,255,0.25); }
.suite-right { padding-top: 18px; }
.suite-list { display: grid; gap: 18px; }
.suite-item { display: grid; grid-template-columns: 40px 1fr; gap: 14px; align-items: start; }
.suite-badge { width: 40px; height: 40px; border-radius: 10px; border: 2px solid #4C6FFF; background: #F4F7FF; display: inline-flex; align-items: center; justify-content: center; color: #4C6FFF; font-size: 18px; box-shadow: 0 6px 16px rgba(76,111,255,0.15); }
.suite-item h4 { margin: 0; font-family: 'Inter', sans-serif; font-weight: 800; font-size: 20px; color: #2F3348; }
.suite-item p { margin-top: 6px; font-size: 14px; color: #5F6783; line-height: 1.5; }
/* CTA section */
.cta-section { background: #1e3a8a; padding: 120px 0; position: relative; overflow: hidden; }
.cta-container { max-width: 1280px; margin: 0 auto; padding: 0 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 80px; align-items: center; }
.cta-left { text-align: left; }
.cta-left h2 { font-family: 'Inter', sans-serif; font-weight: 700; font-size: clamp(40px, 6vw, 64px); color: #fff; margin-bottom: 20px; line-height: 1.2; }
.cta-subtitle { color: #fff; opacity: 0.9; font-size: clamp(18px, 2.5vw, 24px); margin-bottom: 32px; line-height: 1.4; }
.cta-button-large { background: rgba(59, 130, 246, 0.2); color: #fff; padding: 18px 40px; border-radius: 50px; border: 2px solid #60a5fa; text-decoration: none; font-weight: 600; font-size: clamp(16px, 2vw, 20px); display: inline-block; transition: all 0.3s ease; }
.cta-button-large:hover { background: rgba(59, 130, 246, 0.3); transform: translateY(-2px); }

.cta-right { position: relative; height: 400px; display: flex; align-items: center; justify-content: center; }
.character-bottom { width: 100%; height: 100%; background: url('/images/character-bottom.png') center/contain no-repeat; opacity: 0.9; }

/* Footer */
.footer { background:#111827; color:#fff; padding: 57px 0 28px; }
.footer-container { max-width: 1280px; margin: 0 auto; padding: 0 28px; }
.footer-content { display:grid; grid-template-columns: 1fr 1fr; gap: 214px; margin-bottom: 43px; }
.footer-brand { display:flex; align-items:center; gap:8px; margin-bottom:15px; }
.footer .logo-image { width: 56px; height: 56px; object-fit: contain; }
.footer-tagline { font-family:'Inter', sans-serif; font-weight:400; font-size:14.23px; color:#9CA3AF; margin-bottom:30px; }
.social-links { display:flex; gap:14px; }
.social-link { width:35.57px; height:35.57px; background:#1F2937; border-radius:14.23px; display:flex; align-items:center; justify-content:center; color:#fff; text-decoration:none; transition:all .3s ease; }
.social-link:hover { background:#3F51B5; transform: translateY(-2px); }
.download-section h3 { font-family:'Inter', sans-serif; font-weight:600; font-size:16.01px; color:#fff; margin-bottom:14px; }
.download-description { font-family:'Inter', sans-serif; font-weight:400; font-size:14.23px; color:#9CA3AF; margin-bottom:14px; }
.download-buttons { display:flex; flex-direction:column; gap:10.67px; }
.download-button { background:#1F2937; border-radius:7.11px; padding:10.67px; display:flex; align-items:center; gap:10.67px; text-decoration:none; color:#fff; transition:all .3s ease; }
.download-button:hover { background:#374151; transform: translateY(-2px); }
.download-button i { font-size:21.34px; }
.download-info { display:flex; flex-direction:column; }
.download-type { font-family:'Inter', sans-serif; font-weight:400; font-size:10.67px; color:#9CA3AF; }
.download-store { font-family:'Inter', sans-serif; font-weight:400; font-size:12.45px; color:#fff; }
.footer-bottom { border-top: 1px solid #1F2937; padding-top:29.34px; text-align:center; }
.copyright { font-family:'Inter', sans-serif; font-weight:400; font-size:12.45px; color:#6B7280; }

/* Animations */
@keyframes fadeInUp { from { opacity:0; transform: translateY(30px); } to { opacity:1; transform: translateY(0); } }
.animate-fade-in-up { animation: fadeInUp 0.6s ease-out; }

/* Responsive tweaks */
@media (max-width: 768px) {
  .hero { padding-top: 90px; min-height: auto; }
  .hero-philosophy { padding: 24px 16px; }
  .hero-content { grid-template-columns: 1fr; gap: 40px; text-align: center; }
  .hero-text { text-align: center; }
  .hero-prototype-image { max-width: 400px; }
  .hero-bottom { padding: 60px 0; margin-top: 40px; }
  
  .nav-container { gap: 12px; }
  .nav-pill-image { height: 48px; }
  
  .menu-divider-image { min-height: 150px; }
  .menu-title { font-size: clamp(24px, 5vw, 36px); }
  .menu-subtitle { font-size: clamp(12px, 3vw, 16px); }
  .character-bottom { display:none; }
  .section-container { grid-template-columns: 1fr; gap: 50px; text-align: center; }
  .suite-illustration { height: 260px; }
  .footer-content { grid-template-columns: 1fr; gap: 50px; text-align: center; }
  
  /* Navigation responsive */
  .navbar-left { gap: 20px; }
  .nav-menu { gap: 2px; }
  .nav-item { padding: 5px 12px; font-size: 12px; }
  
  /* CTA responsive */
  .cta-container { grid-template-columns: 1fr; gap: 40px; text-align: center; }
  .cta-left { text-align: center; }
  .cta-right { height: 300px; }
}
</style>
