import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  // Set base path for history mode to work under subpaths in production.
  // Change to '/thinkyai/' (or your deployed subpath). Defaults to '/'.
  base: process.env.VITE_BASE || '/',
  plugins: [vue()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'axios', 'marked']
        }
      }
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  server: {
    port: 3000,
    cors: true,
    proxy: {
      // Proxy Faster-Whisper service in dev
      '/whisper': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/whisper/, '')
      }
    }
  }
})
